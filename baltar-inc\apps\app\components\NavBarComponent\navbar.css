/* Base Navbar */
.navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #f3f4f6;
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.navbar-content {
  max-width: 1150px;
  margin: 0 auto;
  display: flex;

  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

/* Logo */
.logo-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #1f2937;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
}

/* Hamburger */
/* Hamburger Button Style */
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  gap: 5px;
  background: none;
  border: none;
  border-radius: 6px;
  padding: 0px;
  position: absolute; /* 👉 Important */
  top: 1rem;           /* 👉 thoda upar */
  right: 6rem;       /* 👉 screen ke thoda andar */
  z-index: 2000;       /* 👉 hamesha upar dikhna chahiye */
}

/* Hamburger lines */
.bar {
  width: 25px;
  height: 3px;
  background-color: #333;
  border-radius: 2px;
}

/* Navbar links */
.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-item-wrapper {
  position: relative;
}

.nav-item {
  font-weight: 500;
  color: #4b5563;
  text-decoration: none;
  cursor: pointer;
}

.nav-item:hover {
  color: #111827;
}

/* Dropdown */
.dropdown {
  position: absolute;
  top: 2.5rem;
  left: 0;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 200px;
  z-index: 999;
  box-shadow: 0px 4px 12px rgba(0,0,0,0.1);
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #4b5563;
  text-decoration: none;
}

.dropdown-item:hover {
  background-color: #bebeba;
  color: #1f2937;
}

/* 📱 Mobile */
.mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 80%;
  height: 100%;
  background-color: white;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  box-shadow: -5px 0 15px rgba(0,0,0,0.1);
  z-index: 2000;
  animation: slideIn 0.3s ease-in-out;
}

.menu-slide {
  animation: slideOut 0.3s ease-in-out reverse;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0%); }
}

@keyframes slideOut {
  from { transform: translateX(0%); }
  to { transform: translateX(100%); }
}

/* Mobile Dropdown */
.mobile-dropdown-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-dropdown-toggle {
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  text-align: left;
  cursor: pointer;
}

.mobile-dropdown {
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Close Button */
.close-btn {
  align-self: flex-end;
  font-size: 2rem;
  background: none;
  border: none;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }
  
  .nav-links {
    display: none;
  }

  .nav-links.open {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding-top: 1rem;
  }

  .navbar-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
