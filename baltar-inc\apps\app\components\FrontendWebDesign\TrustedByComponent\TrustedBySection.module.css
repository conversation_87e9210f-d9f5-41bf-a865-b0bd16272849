.trustedSection {
  padding: 6rem 2rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 1.25rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.trustedGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.trustedItem {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.trustedItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.trustedItem:hover::before {
  transform: scaleX(1);
}

.trustedItem:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
  border-color: #667eea;
}

.itemIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.itemTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.itemDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.testimonialPreview {
  max-width: 800px;
  margin: 0 auto;
}

.testimonial {
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  position: relative;
}

.testimonial::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 30px;
  font-size: 4rem;
  color: #667eea;
  font-family: serif;
  line-height: 1;
}

.testimonialText {
  font-size: 1.25rem;
  color: #1e293b;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonialAuthor {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.authorAvatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.authorInfo {
  display: flex;
  flex-direction: column;
}

.authorName {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.authorTitle {
  color: #64748b;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .trustedSection {
    padding: 4rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
  }
  
  .trustedGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
  }
  
  .trustedItem {
    padding: 1.5rem;
  }
  
  .testimonial {
    padding: 2rem;
  }
  
  .testimonialText {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .trustedItem {
    padding: 1.25rem;
  }
  
  .itemIcon {
    font-size: 2rem;
  }
  
  .testimonial {
    padding: 1.5rem;
  }
  
  .testimonialAuthor {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}
