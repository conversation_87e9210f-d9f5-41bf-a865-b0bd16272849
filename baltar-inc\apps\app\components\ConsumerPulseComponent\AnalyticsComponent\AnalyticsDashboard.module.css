.analyticsSection {
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
  color: white;
  min-height: 100vh;
  padding: 2rem 0;
  font-family: 'Helvetica Neue', sans-serif;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.headerContent h1.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #ffffff, #cccccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  color: #cccccc;
  margin: 0;
}

.timeframeSelector {
  display: flex;
  align-items: center;
}

.timeframeSelect {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.8rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.metricCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-2px);
}

.metricIcon {
  font-size: 2rem;
  opacity: 0.8;
}

.metricContent {
  flex: 1;
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.3rem 0;
  color: white;
}

.metricTitle {
  font-size: 0.95rem;
  color: #cccccc;
  margin: 0 0 0.2rem 0;
}

.metricSubtitle {
  font-size: 0.8rem;
  color: #999999;
}

.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.chartCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
}

.chartTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.sentimentChart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sentimentItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
}

.sentimentLabel {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.95rem;
  color: #eeeeee;
}

.sentimentColor {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.sentimentStats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sentimentCount {
  font-weight: 600;
  color: white;
}

.sentimentPercentage {
  font-size: 0.9rem;
  color: #cccccc;
}

.trendChart {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.trendItem {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.trendLabel {
  min-width: 140px;
  font-size: 0.9rem;
  color: #cccccc;
}

.trendBar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.trendFill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease;
}

.trendValue {
  min-width: 40px;
  text-align: right;
  font-weight: 600;
  color: white;
}

.listsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.listCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
}

.listTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.listContent {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.listItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.listItemContent {
  flex: 1;
}

.listItemTitle {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 0.3rem 0;
  color: white;
}

.listItemMeta {
  font-size: 0.85rem;
  color: #999999;
  margin: 0;
}

.listItemValue {
  font-weight: 600;
  color: #ffffff;
  font-size: 0.9rem;
}

.apiSection {
  margin-top: 3rem;
}

.apiCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
}

.apiTitle {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.apiDescription {
  font-size: 1.1rem;
  color: #cccccc;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.apiFeatures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.apiFeature {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  font-size: 0.95rem;
  color: #eeeeee;
}

.apiFeatureIcon {
  font-size: 1.2rem;
}

.apiButton {
  background: linear-gradient(45deg, #ffffff, #cccccc);
  color: black;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apiButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.loadingSpinner {
  text-align: center;
  padding: 4rem 2rem;
  font-size: 1.2rem;
  color: #cccccc;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .headerContent h1.title {
    font-size: 2rem;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .listsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chartCard,
  .listCard,
  .apiCard {
    padding: 1.5rem;
  }

  .apiFeatures {
    grid-template-columns: 1fr;
  }

  .trendItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .trendLabel {
    min-width: auto;
  }

  .trendBar {
    width: 100%;
  }
}
