@tailwind base;
@tailwind components;
@tailwind utilities;

*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --background: #0B0F19;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0B0F19;
    --foreground: rgb(0, 0, 0);
  }
}

html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  font-family: Arial, Helvetica, sans-serif;
  background-color: var(--background);
  color: var(--foreground);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  background-color: #1f2937;
  color: white;
  border: 1px solid #374151;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background-color: #4f46e5;
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover {
  background-color: #4338ca;
}

html {
  scroll-behavior: smooth;
}
