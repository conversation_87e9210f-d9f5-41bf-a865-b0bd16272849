.dashboardContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  color: white;
}

.headerContent h1 {
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
}

.headerContent p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.supportButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.supportButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.tabNavigation {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 0.5rem;
}

.tab {
  flex: 1;
  padding: 1rem 2rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
}

.content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  min-height: 600px;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.summaryCard {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.summaryCard h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.summaryStats {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.stat {
  text-align: center;
  flex: 1;
}

.statNumber {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.financialStats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.financialItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.financialItem:last-child {
  border-bottom: none;
}

.financialLabel {
  font-weight: 500;
  color: #374151;
}

.financialAmount {
  font-weight: 700;
  font-size: 1.1rem;
  color: #1f2937;
}

.recentActivity {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.activitySection h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.projectList,
.invoiceList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.projectItem,
.invoiceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.projectInfo,
.invoiceInfo {
  flex: 1;
}

.projectInfo h4,
.invoiceInfo h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-weight: 600;
}

.projectInfo p,
.invoiceInfo p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.projectDate {
  font-size: 0.75rem;
  color: #9ca3af;
}

.invoiceAmount {
  font-weight: 700;
  color: #1f2937;
  font-size: 1.1rem;
}

.projectStatus,
.invoiceActions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.invoiceStatus {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

.downloadButton {
  background: #3B82F6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.downloadButton:hover {
  background: #2563EB;
  transform: translateY(-1px);
}

/* Table Styles */
.tableContainer {
  overflow-x: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.table tr:hover {
  background: #f9fafb;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.overviewGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.overviewSection {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.overviewSection h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.itemList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.listItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.itemInfo h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
  font-weight: 600;
}

.itemInfo p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.itemDate {
  font-size: 0.75rem;
  color: #9ca3af;
}

.itemStatus {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .headerContent h1 {
    font-size: 2rem;
  }

  .tabNavigation {
    flex-direction: column;
  }

  .tab {
    padding: 0.75rem 1rem;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .recentActivity {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .overviewGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .projectItem,
  .invoiceItem,
  .listItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .projectStatus,
  .invoiceStatus,
  .itemStatus {
    align-self: flex-end;
  }

  .table {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
}
