globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/components/layoutClient.js":{"*":{"id":"(ssr)/./app/components/layoutClient.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/ContactSection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/ContactSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/EngineeringSection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/EngineeringSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/FashionSection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/FashionSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/FinanceSection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/FinanceSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/HospitalitySection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/HospitalitySection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/MediaSection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/MediaSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/TechnologiesSection.js":{"*":{"id":"(ssr)/./app/components/BaltarSections/TechnologiesSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/HeroComponent/HeroSection.js":{"*":{"id":"(ssr)/./app/components/HeroComponent/HeroSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.js":{"*":{"id":"(ssr)/./app/admin/dashboard/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.js":{"*":{"id":"(ssr)/./app/admin/page.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\layoutClient.js":{"id":"(app-pages-browser)/./app/components/layoutClient.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\ContactSection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/ContactSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\EngineeringSection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/EngineeringSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\FashionSection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/FashionSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\FinanceSection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/FinanceSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\HospitalitySection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/HospitalitySection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\MediaSection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/MediaSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\BaltarSections\\TechnologiesSection.js":{"id":"(app-pages-browser)/./app/components/BaltarSections/TechnologiesSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\HeroComponent\\HeroSection.js":{"id":"(app-pages-browser)/./app/components/HeroComponent/HeroSection.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\admin\\dashboard\\page.js":{"id":"(app-pages-browser)/./app/admin/dashboard/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\admin\\page.js":{"id":"(app-pages-browser)/./app/admin/page.js","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\":[],"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./app/components/layoutClient.js":{"*":{"id":"(rsc)/./app/components/layoutClient.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/ContactSection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/ContactSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/EngineeringSection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/EngineeringSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/FashionSection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/FashionSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/FinanceSection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/FinanceSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/HospitalitySection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/HospitalitySection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/MediaSection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/MediaSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/BaltarSections/TechnologiesSection.js":{"*":{"id":"(rsc)/./app/components/BaltarSections/TechnologiesSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/HeroComponent/HeroSection.js":{"*":{"id":"(rsc)/./app/components/HeroComponent/HeroSection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.js":{"*":{"id":"(rsc)/./app/admin/dashboard/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.js":{"*":{"id":"(rsc)/./app/admin/page.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}