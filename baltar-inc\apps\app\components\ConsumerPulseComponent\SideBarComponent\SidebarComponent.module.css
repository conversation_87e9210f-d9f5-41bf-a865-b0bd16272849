.sidebarContainer {
  position: fixed;
  top: 150px; /* Just below header */
  right: 0;
  z-index: 9999;
}

.toggleButton {
  position: absolute;
  top: 0;
  background: #000;
  color: white;
  border: none;
  padding: 12px 24px; /* bigger */
  font-weight: bold;
  cursor: pointer;
  transform: rotate(-90deg);
  transform-origin: top right;
  left: -110px; /* Try increasing or decreasing this if off screen */
  border-radius: 6px 0 0 6px; /* Rounded left corners */
  transform: rotate(0deg); /* ✅ Make it horizontal */
  writing-mode: horizontal-tb; /* ensure horizontal mode */
  box-shadow: -2px 2px 10px rgba(0,0,0,0.2);
  transition: background 0.3s ease;
  z-index: 10000;
  border: 2px solid white;
  
}

.sidebar {
  width: 300px;
  background-color: white;
  border-left: 1px solid #e0e0e0;
  height: calc(100vh - 80px);
  overflow-y: auto;
  box-shadow: -5px 0 10px rgba(0,0,0,0.05);
  padding: 1rem 1.2rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h3 {
  font-size: 1.2rem;
  color: #e00000;
  font-weight: 600;
}

.category {
  font-size: 0.85rem;
}

.newsList {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.newsList li {
  display: flex;
  flex-direction: column;
}

.time {
  font-size: 0.75rem;
  color: #888;
  margin-bottom: 0.25rem;
}

.title {
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
}

.title:hover {
  text-decoration: underline;
}

.viewAll {
  display: block;
  margin-top: 1rem;
  font-size: 0.85rem;
  text-align: right;
  color: #333;
  text-decoration: none;
}

@media (max-width: 768px) {
  .closeButton {
    position: left;
    top: 1rem;
    right: 0.5rem;
    z-index: 1001;
    font-size: 1.5rem;
    background: none;
    border: none;
    color: Black;
    cursor: pointer;
  }

}
 