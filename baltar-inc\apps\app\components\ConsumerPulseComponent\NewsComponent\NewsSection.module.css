.newsSection {
    padding: 4rem 2rem;
    background: #fff;
    border-top: 1px solid #e0e0e0;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .titleSection {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .title {
    font-size: 1.8rem;
    font-weight: 700;
    font-family: 'Helvetica Neue', sans-serif;
    color: #111;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .lastUpdated {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
    margin-top: 0.5rem;
  }

  .liveIndicator {
    animation: pulse 2s infinite;
    font-size: 0.8rem;
    color: #dc2626;
  }

  .connectionStatus {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    background: rgba(0, 0, 0, 0.05);
  }

  .statusDot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
  }

  .statusDot.connected {
    background: #16a34a;
    animation: pulse 2s infinite;
  }

  .statusDot.connecting {
    background: #d97706;
    animation: pulse 1s infinite;
  }

  .statusDot.error {
    background: #dc2626;
  }

  .connectionStatus.connected {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
  }

  .connectionStatus.connecting {
    background: rgba(251, 191, 36, 0.1);
    color: #d97706;
  }

  .connectionStatus.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
  }

  .controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .updateBadge {
    font-size: 0.75rem;
    font-weight: 600;
    color: #dc2626;
    background: rgba(239, 68, 68, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.75rem;
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }



  .refreshButton {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
  }

  .refreshButton:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
  }

  .refreshButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .refreshNewsButton {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-right: 0.5rem;
  }

  .refreshNewsButton:hover:not(:disabled) {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  }

  .refreshNewsButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .loading {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
    font-size: 1.1rem;
  }

  .articlesGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
  }

  .noArticles {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    border: 1px dashed #d1d5db;
  }

  .noArticles p {
    margin: 0.5rem 0;
    font-size: 1rem;
  }

  .noArticles p:first-child {
    font-weight: 600;
    color: #374151;
  }

  .articleCard {
    display: flex;
    flex-direction: column;
    height: 400px; /* Fixed height for consistent layout */
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
  }

  .articleCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .articleCard.breaking {
    border-color: #dc2626;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.1);
  }

  .imageContainer {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
  }

  .articleImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
  }

  .articleCard:hover .articleImage {
    transform: scale(1.05);
  }

  .breakingBadge {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: #dc2626;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 1;
  }

  .articleContent {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 1rem;
  }

  .articleTitle {
    font-size: 1.1rem;
    font-weight: 600;
    color: #000;
    font-family: 'Helvetica Neue', sans-serif;
    line-height: 1.3;
    margin: 0 0 0.5rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .articleDescription {
    font-size: 0.9rem;
    color: #555;
    line-height: 1.5;
    margin: 0 0 1rem 0;
    flex: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .articleMeta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid #f0f0f0;
    gap: 0.5rem;
  }

  .category {
    background: #f0f0f0;
    color: #333;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
  }

  .publishedAt {
    font-size: 0.75rem;
    color: #666;
    font-style: italic;
  }



  /* Responsive Design */
  @media (max-width: 768px) {
    .newsSection {
      padding: 2rem 1rem;
    }

    .articlesGrid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .controls {
      justify-content: center;
    }

    .articleCard {
      max-width: 100%;
      height: auto;
      min-height: 350px;
    }

    .articleTitle {
      font-size: 1rem;
      -webkit-line-clamp: 2;
    }

    .articleContent {
      padding: 1rem;
    }

    .title {
      font-size: 1.5rem;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .articlesGrid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1025px) {
    .articlesGrid {
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
  }

  /* Modal Styles */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modalContent {
    background: white;
    border-radius: 12px;
    max-width: 800px;
    max-height: 90vh;
    width: 100%;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .closeButton {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
  }

  .closeButton:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  .modalImage {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 12px 12px 0 0;
  }

  .modalTitle {
    font-size: 1.8rem;
    font-weight: 700;
    color: #111;
    margin: 1.5rem 2rem 1rem 2rem;
    line-height: 1.3;
  }

  .modalMeta {
    display: flex;
    gap: 1rem;
    margin: 0 2rem 1.5rem 2rem;
    flex-wrap: wrap;
    align-items: center;
  }

  .modalCategory {
    background: #f0f0f0;
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: capitalize;
  }

  .modalDate {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
  }

  .modalDescription {
    font-size: 1.1rem;
    color: #444;
    line-height: 1.6;
    margin: 0 2rem 1.5rem 2rem;
  }

  .sourceLink {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: background 0.2s ease;
    margin: 0 2rem 2rem 2rem;
  }

  .sourceLink:hover {
    background: #0056b3;
    color: white;
  }

  /* Modal responsive styles */
  @media (max-width: 768px) {
    .modalContent {
      margin: 1rem;
      max-height: 95vh;
    }

    .modalImage {
      height: 200px;
    }

    .modalTitle {
      font-size: 1.5rem;
      margin: 1rem 1.5rem 0.75rem 1.5rem;
    }

    .modalMeta {
      margin: 0 1.5rem 1rem 1.5rem;
    }

    .modalDescription {
      font-size: 1rem;
      margin: 0 1.5rem 1.5rem 1.5rem;
    }

    .sourceLink {
      margin: 0 1.5rem 1.5rem 1.5rem;
    }
  }
