.lookbookSection {
  width: 100%;
  background: #000;
  padding: 4rem 2rem;
  color: white;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* --- CATEGORY BUTTONS --- */
.categories {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 3rem;
  justify-content: center;
}

.categoryButton {
  font-size: 1.5rem;
  font-weight: 300;
  background: white;
  color: black;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 50px;
  text-transform: capitalize;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.categoryButton:hover {
  background: black;
  color: white;
  border: 1px solid white;
}

.active {
  background: black;
  color: white;
  border: 1px solid white;
}

/* --- GALLERY --- */
.gallery {
  display: flex;
  gap: 1.5rem;
  padding: 1rem 0;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
}

.gallery::-webkit-scrollbar {
  display: none; /* Chrome */
}

.galleryImage {
  height: 300px;
  width: 200px;
  flex-shrink: 0;
  border-radius: 10px;
  object-fit: cover;
}

/* --- PRODUCT ITEMS --- */
.productItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.productName {
  font-size: 0.9rem;
  font-weight: 300;
  text-align: center;
  color: white;
  max-width: 200px;
  word-wrap: break-word;
}

.noImage {
  height: 300px;
  width: 200px;
  flex-shrink: 0;
  border-radius: 10px;
  background: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 0.9rem;
}

/* --- LOADING AND ERROR STATES --- */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #666;
  font-size: 1.1rem;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  color: #ff6b6b;
  text-align: center;
}

.error button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.error button:hover {
  background: #ff5252;
}

.noProducts {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #666;
  text-align: center;
  width: 100%;
}

/* 📱 Mobile Styles */
@media (max-width: 768px) {
  .categories {
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 1rem;
    padding-bottom: 1rem;
    justify-content: flex-start;
    scrollbar-width: none;
  }

  .categories::-webkit-scrollbar {
    display: none;
  }

  .categoryButton {
    flex: 0 0 auto;
    font-size: 1.2rem;
    padding: 0.6rem 1.2rem;
  }

  .gallery {
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 1rem;
    justify-content: flex-start;
  }

  .galleryImage {
    height: 250px;
    width: 180px;
  }

  .noImage {
    height: 250px;
    width: 180px;
  }

  .productName {
    font-size: 0.8rem;
    max-width: 180px;
  }
}
