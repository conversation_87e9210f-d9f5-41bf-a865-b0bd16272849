/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/ContactSection.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/* Contact Section - Meta Style */
.ContactSection_section__qZyjV {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.ContactSection_videoContainer__k1o26 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.ContactSection_backgroundVideo__GWl7d {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.3) contrast(1.2) saturate(1.1);
}

.ContactSection_videoOverlay__uHzum {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 2;
}

.ContactSection_content__U0VA7 {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.ContactSection_header__zB7_C {
  text-align: center;
  margin-bottom: 4rem;
}

.ContactSection_sectionTitle__1GvOm {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ContactSection_sectionSubtitle__za_4a {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.ContactSection_ctaContainer__Sbox5 {
  text-align: center;
  margin-bottom: 4rem;
}

.ContactSection_ctaDescription__1RMcV {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.ContactSection_contactGrid__mb8fG {
  display: flex;
  justify-content: center;
  margin: 0 auto;
}

.ContactSection_contactCard__7Qfy_ {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.4s ease;
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.ContactSection_contactCard__7Qfy_:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(96, 165, 250, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.ContactSection_contactCard__7Qfy_ h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.ContactSection_contactCard__7Qfy_ p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.ContactSection_primaryButton__EGxLi, .ContactSection_secondaryButton__ISEJx {
  display: inline-block;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.ContactSection_primaryButton__EGxLi {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: white;
}

.ContactSection_primaryButton__EGxLi:hover {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(96, 165, 250, 0.3);
}

.ContactSection_secondaryButton__ISEJx {
  background: transparent;
  color: rgba(96, 165, 250, 0.9);
  border-color: rgba(96, 165, 250, 0.5);
}

.ContactSection_secondaryButton__ISEJx:hover {
  background: rgba(96, 165, 250, 0.1);
  color: white;
  border-color: rgba(96, 165, 250, 0.8);
  transform: translateY(-2px);
}



/* Responsive Design */
@media (max-width: 768px) {
  .ContactSection_section__qZyjV {
    padding: 4rem 0;
  }
  
  .ContactSection_content__U0VA7 {
    padding: 0 1rem;
  }
  
  .ContactSection_header__zB7_C {
    margin-bottom: 2rem;
  }
  
  .ContactSection_ctaContainer__Sbox5 {
    margin-bottom: 2rem;
  }



  .ContactSection_contactCard__7Qfy_ {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .ContactSection_contactCard__7Qfy_ {
    padding: 1.25rem;
  }
  
  .ContactSection_primaryButton__EGxLi, .ContactSection_secondaryButton__ISEJx {
    padding: 0.625rem 1.5rem;
    font-size: 0.9rem;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/EngineeringSection.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/* Archon Engineering Section - Meta Style */
.EngineeringSection_section__4F2sU {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.EngineeringSection_videoContainer__mSPa_ {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.EngineeringSection_backgroundVideo__9CO4v {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.EngineeringSection_videoOverlay__uykVt {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(100, 100, 100, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(80, 80, 80, 0.2) 100%
  );
  z-index: 2;
}

.EngineeringSection_content__dHFRk {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.EngineeringSection_header__ot9kZ {
  text-align: center;
  margin-bottom: 4rem;
}

.EngineeringSection_sectionTitle__lp0KW {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #9ca3af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.EngineeringSection_sectionSubtitle__cRkMb {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.EngineeringSection_serviceContainer__fSKoN {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.EngineeringSection_mainServiceCard__zi3_I {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 3rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
}

.EngineeringSection_mainServiceCard__zi3_I::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(156, 163, 175, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.EngineeringSection_mainServiceCard__zi3_I:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(156, 163, 175, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.EngineeringSection_mainServiceCard__zi3_I:hover::before {
  opacity: 1;
}

.EngineeringSection_intro__XRSO2 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  text-align: center;
}

.EngineeringSection_featuresGrid__cV7op {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.EngineeringSection_featureColumn__EyMmR h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgba(156, 163, 175, 0.9);
  margin-bottom: 1rem;
  text-align: center;
}

.EngineeringSection_featureList__5wcHE {
  list-style: none;
  padding: 0;
  margin: 0;
}

.EngineeringSection_featureList__5wcHE li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.EngineeringSection_featureList__5wcHE li::before {
  content: '🏗️';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.EngineeringSection_serviceDescription__M4Bt3 {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(156, 163, 175, 0.1);
  border-left: 3px solid rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  text-align: center;
}

.EngineeringSection_serviceLink__gs8mA {
  display: inline-flex;
  align-items: center;
  color: rgba(156, 163, 175, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
  justify-content: center;
  width: 100%;
}

.EngineeringSection_serviceLink__gs8mA:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .EngineeringSection_section__4F2sU {
    padding: 4rem 0;
  }
  
  .EngineeringSection_content__dHFRk {
    padding: 0 1rem;
  }
  
  .EngineeringSection_header__ot9kZ {
    margin-bottom: 2rem;
  }
  
  .EngineeringSection_mainServiceCard__zi3_I {
    padding: 2rem;
  }
  
  .EngineeringSection_featuresGrid__cV7op {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .EngineeringSection_mainServiceCard__zi3_I {
    padding: 1.5rem;
  }
  
  .EngineeringSection_featuresGrid__cV7op {
    gap: 1rem;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/FashionSection.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/* Baltar Fashion Section - Meta Style */
.FashionSection_section__YsW0P {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.FashionSection_videoContainer__idSt5 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.FashionSection_backgroundVideo__k9IbK {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.FashionSection_videoOverlay__Snx5e {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(200, 0, 100, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(150, 0, 150, 0.2) 100%
  );
  z-index: 2;
}

.FashionSection_content__A9cX1 {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.FashionSection_header___VIW1 {
  text-align: center;
  margin-bottom: 4rem;
}

.FashionSection_sectionTitle__Xb9kz {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.FashionSection_sectionSubtitle__t8JTA {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.FashionSection_servicesGrid__FRg7b {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.FashionSection_serviceCard__eDUt3 {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2.5rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.FashionSection_serviceCard__eDUt3::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(236, 72, 153, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.FashionSection_serviceCard__eDUt3:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(236, 72, 153, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.FashionSection_serviceCard__eDUt3:hover::before {
  opacity: 1;
}

.FashionSection_cardContent__88XA4 h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.FashionSection_cardContent__88XA4 > p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.FashionSection_featureList__LbO5R {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.FashionSection_featureList__LbO5R li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.FashionSection_featureList__LbO5R li::before {
  content: '✨';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.FashionSection_serviceDescription__Evb0z {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(236, 72, 153, 0.1);
  border-left: 3px solid rgba(236, 72, 153, 0.5);
  border-radius: 4px;
}

.FashionSection_serviceLink__5ct74 {
  display: inline-flex;
  align-items: center;
  color: rgba(236, 72, 153, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.FashionSection_serviceLink__5ct74:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .FashionSection_servicesGrid__FRg7b {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .FashionSection_serviceCard__eDUt3 {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .FashionSection_section__YsW0P {
    padding: 4rem 0;
  }
  
  .FashionSection_content__A9cX1 {
    padding: 0 1rem;
  }
  
  .FashionSection_header___VIW1 {
    margin-bottom: 2rem;
  }
  
  .FashionSection_serviceCard__eDUt3 {
    padding: 1.5rem;
  }
  
  .FashionSection_servicesGrid__FRg7b {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .FashionSection_servicesGrid__FRg7b {
    grid-template-columns: 1fr;
  }
  
  .FashionSection_serviceCard__eDUt3 {
    padding: 1.25rem;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/FinanceSection.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/* Baltar Finance Section - Meta Style */
.FinanceSection_section__i3QLD {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.FinanceSection_videoContainer__9oLJG {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.FinanceSection_backgroundVideo__mXo52 {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.FinanceSection_videoOverlay__UEA_S {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 150, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 100, 0, 0.2) 100%
  );
  z-index: 2;
}

.FinanceSection_content__0suyH {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.FinanceSection_header__3eNnP {
  text-align: center;
  margin-bottom: 4rem;
}

.FinanceSection_sectionTitle__snwKY {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.FinanceSection_sectionSubtitle__DoxXT {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.FinanceSection_serviceContainer__FMuKl {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.FinanceSection_mainServiceCard__JB65i {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 3rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
}

.FinanceSection_mainServiceCard__JB65i::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.FinanceSection_mainServiceCard__JB65i:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.FinanceSection_mainServiceCard__JB65i:hover::before {
  opacity: 1;
}

.FinanceSection_cardContent__VPLkZ h3 {
  font-size: 2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
  text-align: center;
}

.FinanceSection_intro__4cUGr {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  text-align: center;
}

.FinanceSection_featuresGrid__0ygwC {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.FinanceSection_featureColumn__b0O_o h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgba(16, 185, 129, 0.9);
  margin-bottom: 1rem;
  text-align: center;
}

.FinanceSection_featureList__NN333 {
  list-style: none;
  padding: 0;
  margin: 0;
}

.FinanceSection_featureList__NN333 li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.FinanceSection_featureList__NN333 li::before {
  content: '💰';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.FinanceSection_serviceDescription__YAomU {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(16, 185, 129, 0.1);
  border-left: 3px solid rgba(16, 185, 129, 0.5);
  border-radius: 4px;
  text-align: center;
}

.FinanceSection_serviceLink__Zo2_z {
  display: inline-flex;
  align-items: center;
  color: rgba(16, 185, 129, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
  justify-content: center;
  width: 100%;
}

.FinanceSection_serviceLink__Zo2_z:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .FinanceSection_section__i3QLD {
    padding: 4rem 0;
  }
  
  .FinanceSection_content__0suyH {
    padding: 0 1rem;
  }
  
  .FinanceSection_header__3eNnP {
    margin-bottom: 2rem;
  }
  
  .FinanceSection_mainServiceCard__JB65i {
    padding: 2rem;
  }
  
  .FinanceSection_featuresGrid__0ygwC {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .FinanceSection_mainServiceCard__JB65i {
    padding: 1.5rem;
  }
  
  .FinanceSection_featuresGrid__0ygwC {
    gap: 1rem;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/HospitalitySection.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/* Baltar Hospitality Section - Meta Style */
.HospitalitySection_section__rse1p {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.HospitalitySection_videoContainer__7Vj_I {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.HospitalitySection_backgroundVideo__m8Gco {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.HospitalitySection_videoOverlay__WjgP6 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(200, 100, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(150, 50, 0, 0.2) 100%
  );
  z-index: 2;
}

.HospitalitySection_content__mMUdQ {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.HospitalitySection_header__t9hp9 {
  text-align: center;
  margin-bottom: 4rem;
}

.HospitalitySection_sectionTitle__A_ga3 {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.HospitalitySection_sectionSubtitle__ffGJB {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.HospitalitySection_serviceContainer__YHSfW {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.HospitalitySection_mainServiceCard__IE4d7 {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 3rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
}

.HospitalitySection_mainServiceCard__IE4d7::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.HospitalitySection_mainServiceCard__IE4d7:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.HospitalitySection_mainServiceCard__IE4d7:hover::before {
  opacity: 1;
}

.HospitalitySection_cardContent__3eBV2 h3 {
  font-size: 2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
  text-align: center;
}

.HospitalitySection_cardContent__3eBV2 > p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  text-align: center;
}

.HospitalitySection_featuresGrid__rmMbr {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.HospitalitySection_featureColumn__1eDRq h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgba(245, 158, 11, 0.9);
  margin-bottom: 1rem;
  text-align: center;
}

.HospitalitySection_featureList__H_EvQ {
  list-style: none;
  padding: 0;
  margin: 0;
}

.HospitalitySection_featureList__H_EvQ li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.HospitalitySection_featureList__H_EvQ li::before {
  content: '🍽️';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.HospitalitySection_serviceDescription__6QtSA {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(245, 158, 11, 0.1);
  border-left: 3px solid rgba(245, 158, 11, 0.5);
  border-radius: 4px;
  text-align: center;
}

.HospitalitySection_serviceLink__fEUy9 {
  display: inline-flex;
  align-items: center;
  color: rgba(245, 158, 11, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
  justify-content: center;
  width: 100%;
}

.HospitalitySection_serviceLink__fEUy9:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .HospitalitySection_section__rse1p {
    padding: 4rem 0;
  }
  
  .HospitalitySection_content__mMUdQ {
    padding: 0 1rem;
  }
  
  .HospitalitySection_header__t9hp9 {
    margin-bottom: 2rem;
  }
  
  .HospitalitySection_mainServiceCard__IE4d7 {
    padding: 2rem;
  }
  
  .HospitalitySection_featuresGrid__rmMbr {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .HospitalitySection_mainServiceCard__IE4d7 {
    padding: 1.5rem;
  }
  
  .HospitalitySection_featuresGrid__rmMbr {
    gap: 1rem;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/MediaSection.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/* Baltar Media Section - Meta Style */
.MediaSection_section__6EhGB {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.MediaSection_videoContainer__JEhTP {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.MediaSection_backgroundVideo__VgEEJ {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.MediaSection_videoOverlay__OyBlH {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(100, 0, 200, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(50, 0, 150, 0.2) 100%
  );
  z-index: 2;
}

.MediaSection_content__Dc7aK {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.MediaSection_header__CXgux {
  text-align: center;
  margin-bottom: 4rem;
}

.MediaSection_sectionTitle__MfoFu {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.MediaSection_sectionSubtitle__NBbrw {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.MediaSection_servicesGrid__wY4my {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.MediaSection_serviceCard__I5EkP {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2.5rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.MediaSection_serviceCard__I5EkP::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.MediaSection_serviceCard__I5EkP:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.MediaSection_serviceCard__I5EkP:hover::before {
  opacity: 1;
}

.MediaSection_cardContent__M5FL5 h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.MediaSection_cardContent__M5FL5 > p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.MediaSection_featureList__uyD73 {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.MediaSection_featureList__uyD73 li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.MediaSection_featureList__uyD73 li::before {
  content: '📊';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.MediaSection_serviceDescription__t_iIw {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(139, 92, 246, 0.1);
  border-left: 3px solid rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

.MediaSection_serviceLink__4Keqj {
  display: inline-flex;
  align-items: center;
  color: rgba(139, 92, 246, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.MediaSection_serviceLink__4Keqj:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .MediaSection_servicesGrid__wY4my {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .MediaSection_serviceCard__I5EkP {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .MediaSection_section__6EhGB {
    padding: 4rem 0;
  }
  
  .MediaSection_content__Dc7aK {
    padding: 0 1rem;
  }
  
  .MediaSection_header__CXgux {
    margin-bottom: 2rem;
  }
  
  .MediaSection_serviceCard__I5EkP {
    padding: 1.5rem;
  }
  
  .MediaSection_servicesGrid__wY4my {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .MediaSection_servicesGrid__wY4my {
    grid-template-columns: 1fr;
  }
  
  .MediaSection_serviceCard__I5EkP {
    padding: 1.25rem;
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/BaltarSections/TechnologiesSection.module.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/* Baltar Technologies Section - Meta Style */
.TechnologiesSection_section__Nvye9 {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.TechnologiesSection_videoContainer__YG1dP {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.TechnologiesSection_backgroundVideo__FJFH9 {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.TechnologiesSection_videoOverlay__pWlSB {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 100, 200, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 50, 150, 0.2) 100%
  );
  z-index: 2;
}

.TechnologiesSection_content__75_H5 {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.TechnologiesSection_header__WAiSu {
  text-align: center;
  margin-bottom: 4rem;
}

.TechnologiesSection_sectionTitle__YdDyU {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #60a5fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.TechnologiesSection_sectionTitle__YdDyU::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #60a5fa, transparent);
  border-radius: 2px;
  animation: TechnologiesSection_pulse__llDrw 2s ease-in-out infinite;
}

@keyframes TechnologiesSection_pulse__llDrw {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-50%) scaleX(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scaleX(1.2);
  }
}

.TechnologiesSection_sectionSubtitle__iJCWC {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.TechnologiesSection_servicesGrid__uJkli {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.TechnologiesSection_serviceCard__1o4GF {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2.5rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.TechnologiesSection_serviceCard__1o4GF::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.TechnologiesSection_serviceCard__1o4GF:hover {
  transform: translateY(-12px) scale(1.02);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(96, 165, 250, 0.3);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(96, 165, 250, 0.2);
}

.TechnologiesSection_serviceCard__1o4GF:hover::before {
  opacity: 1;
}

.TechnologiesSection_cardContent__q9VmU h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

.TechnologiesSection_cardContent__q9VmU > p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.TechnologiesSection_featureList__nza6X {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.TechnologiesSection_featureList__nza6X li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.TechnologiesSection_featureList__nza6X li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: rgba(96, 165, 250, 0.8);
  font-weight: bold;
}

.TechnologiesSection_serviceDescription__s8v9G {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 1.5rem 0;
  padding: 1rem;
  background: rgba(96, 165, 250, 0.1);
  border-left: 3px solid rgba(96, 165, 250, 0.5);
  border-radius: 4px;
}

.TechnologiesSection_serviceLink__tSUJi {
  display: inline-flex;
  align-items: center;
  color: rgba(96, 165, 250, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.TechnologiesSection_serviceLink__tSUJi:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .TechnologiesSection_servicesGrid__uJkli {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .TechnologiesSection_serviceCard__1o4GF {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .TechnologiesSection_section__Nvye9 {
    padding: 4rem 0;
  }
  
  .TechnologiesSection_content__75_H5 {
    padding: 0 1rem;
  }
  
  .TechnologiesSection_header__WAiSu {
    margin-bottom: 2rem;
  }
  
  .TechnologiesSection_serviceCard__1o4GF {
    padding: 1.5rem;
  }
  
  .TechnologiesSection_servicesGrid__uJkli {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .TechnologiesSection_servicesGrid__uJkli {
    grid-template-columns: 1fr;
  }
  
  .TechnologiesSection_serviceCard__1o4GF {
    padding: 1.25rem;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/HeroComponent/HeroSection.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/* Meta-Inspired Hero Section Styles */
.HeroSection_hero__Jrrsh {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 64px; /* Account for fixed header */
}

.HeroSection_videoContainer__poHl5 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.HeroSection_heroVideo__XR8wH {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(0.7) contrast(1.1);
}

.HeroSection_videoOverlay__oulEX {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 2;
}

.HeroSection_heroContent__rRJa5 {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 1200px;
  padding: 0 2rem;
}

.HeroSection_titleContainer__xcd3S {
  margin-bottom: 2rem;
}

.HeroSection_mainTitle__cFSYX {
  font-size: clamp(3.5rem, 8vw, 8rem);
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
}

.HeroSection_subtitle__05gJw {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 300;
  line-height: 1.2;
  margin-bottom: 0;
  letter-spacing: 0.01em;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.HeroSection_description__hws_o {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  max-width: 800px;
  margin: 2rem auto 3rem;
  font-weight: 300;
  text-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}



/* Floating Elements */
.HeroSection_floatingElements__We19v {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.HeroSection_floatingCircle1____lA3 {
  position: absolute;
  top: 20%;
  right: 15%;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.3), rgba(59, 130, 246, 0.2));
  border-radius: 50%;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.HeroSection_floatingCircle2__nB60x {
  position: absolute;
  top: 60%;
  left: 10%;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(251, 191, 36, 0.2));
  border-radius: 50%;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.HeroSection_floatingCircle3__HmK1Y {
  position: absolute;
  top: 30%;
  left: 20%;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.2), rgba(219, 39, 119, 0.1));
  border-radius: 50%;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .HeroSection_heroContent__rRJa5 {
    padding: 0 1rem;
  }

  .HeroSection_description__hws_o {
    margin: 1.5rem auto 2rem;
  }

  .HeroSection_floatingCircle1____lA3 {
    width: 40px;
    height: 40px;
    top: 25%;
    right: 10%;
  }

  .HeroSection_floatingCircle2__nB60x {
    width: 30px;
    height: 30px;
    top: 65%;
    left: 5%;
  }

  .HeroSection_floatingCircle3__HmK1Y {
    width: 50px;
    height: 50px;
    top: 35%;
    left: 15%;
  }
}

@media (max-width: 480px) {
  .HeroSection_titleContainer__xcd3S {
    margin-bottom: 1.5rem;
  }

  .HeroSection_description__hws_o {
    margin: 1rem auto 1.5rem;
  }

  .HeroSection_floatingElements__We19v {
    display: none; /* Hide on very small screens to avoid clutter */
  }
}

