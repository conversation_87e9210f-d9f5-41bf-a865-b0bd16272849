.footer {
  background-color: #0c0c0c;
  color: #ffffff;
  padding: 4rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  overflow-x: hidden;
  max-width: 100vw;
  box-sizing: border-box;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 2rem;
  width: 100%;
  box-sizing: border-box;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: #fff;
}

.logo img {
  width: 36px;
  height: auto;
  filter: brightness(0) invert(1);
  opacity: 0.95;
}

.column h4 {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.column li {
  margin-bottom: 0.5rem;
  color: #d1d5db;
  cursor: pointer;
  transition: color 0.3s ease;
}

.column li:hover {
  color: #ffffff;
}

.social {
  display: flex;
  gap: 1rem;
  font-size: 1.2rem;
  color: #ffffff;
  margin-top: 0.5rem;
}

.trustAndNewsletter {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  border-top: 1px solid #2f2f2f;
  padding-top: 2rem;
}

.trustBadges {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #9ca3af;
  flex: 1;
  min-width: 220px;
}

.newsletter {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
  flex: 1;
  min-width: 260px;
}

.form {
  display: flex;
  gap: 0.5rem;
}

.form input {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 0.375rem;
  box-sizing: border-box;
}

.form button {
  padding: 0.75rem 1rem;
  background-color: #ffffff;
  color: #000000;
  font-weight: 600;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.form button:hover {
  background-color: #f3f4f6;
}

/* 📱 Mobile Responsive */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 1.5rem;
  }

  .trustAndNewsletter {
    flex-direction: column;
    align-items: flex-start;
  }

  .form {
    flex-direction: column;
  }

  .form input,
  .form button {
    width: 100%;
  }

  .logo {
    justify-content: center;
  }
}
