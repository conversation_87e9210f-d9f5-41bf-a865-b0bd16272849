/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/components/layoutClient.js":
/*!****************************************!*\
  !*** ./app/components/layoutClient.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\baltar\\baltar-inc\\apps\\app\\components\\layoutClient.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"42ae881cbd3d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWJjXFxEZXNrdG9wXFxiYWx0YXJcXGJhbHRhci1pbmNcXGFwcHNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0MmFlODgxY2JkM2RcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layoutClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/layoutClient */ \"(rsc)/./app/components/layoutClient.js\");\n\n\n\n\n\nconst metadata = {\n    title: \"Baltar Inc\",\n    description: \"Immersive modern homepage\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                    name: \"viewport\",\n                    content: \"width=device-width, initial-scale=1.0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\",\n                lineNumber: 23,\n                columnNumber: 8\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        overflowX: 'hidden',\n                        width: '100%'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layoutClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\layout.js\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Ccomponents%5C%5ClayoutClient.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Ccomponents%5C%5ClayoutClient.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/layoutClient.js */ \"(rsc)/./app/components/layoutClient.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Ccomponents%5C%5ClayoutClient.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/FooterComponent/Footer.css":
/*!***************************************************!*\
  !*** ./app/components/FooterComponent/Footer.css ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6182cb81e94d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Gb290ZXJDb21wb25lbnQvRm9vdGVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhYmNcXERlc2t0b3BcXGJhbHRhclxcYmFsdGFyLWluY1xcYXBwc1xcYXBwXFxjb21wb25lbnRzXFxGb290ZXJDb21wb25lbnRcXEZvb3Rlci5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2MTgyY2I4MWU5NGRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FooterComponent/Footer.css\n");

/***/ }),

/***/ "(ssr)/./app/components/FooterComponent/Footer.js":
/*!**************************************************!*\
  !*** ./app/components/FooterComponent/Footer.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Footer_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Footer.css */ \"(ssr)/./app/components/FooterComponent/Footer.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"footer\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"footer-content\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"footer-left\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"footer-heading\",\n                        children: [\n                            \"Fill the form \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"highlight\",\n                                children: \"to contact us.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                lineNumber: 11,\n                                columnNumber: 27\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                        lineNumber: 10,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        className: \"contact-form\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"name-fields\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"First Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Last Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"email\",\n                                placeholder: \"E-mail\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                placeholder: \"How can we help you? Describe your problem\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                children: \"Send message\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FooterComponent\\\\Footer.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FooterComponent/Footer.js\n");

/***/ }),

/***/ "(ssr)/./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.css":
/*!******************************************************************************!*\
  !*** ./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.css ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a1f635fde1fd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Gcm9udGVuZFdlYkRlc2lnbi9IZWFkZXJDb21wb25lbnQvSGVhZGVyQ29tcG9uZW50LmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhYmNcXERlc2t0b3BcXGJhbHRhclxcYmFsdGFyLWluY1xcYXBwc1xcYXBwXFxjb21wb25lbnRzXFxGcm9udGVuZFdlYkRlc2lnblxcSGVhZGVyQ29tcG9uZW50XFxIZWFkZXJDb21wb25lbnQuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTFmNjM1ZmRlMWZkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.css\n");

/***/ }),

/***/ "(ssr)/./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.js":
/*!*****************************************************************************!*\
  !*** ./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeaderComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _HeaderComponent_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HeaderComponent.css */ \"(ssr)/./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HeaderComponent() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cursorStyle, setCursorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleMouseMove = (e)=>{\n        if (!isMenuOpen) return;\n        setCursorStyle({\n            left: `${e.clientX - 25}px`,\n            top: `${e.clientY - 25}px`\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeaderComponent.useEffect\": ()=>{\n            if (isMenuOpen) {\n                window.addEventListener('mousemove', handleMouseMove);\n            } else {\n                window.removeEventListener('mousemove', handleMouseMove);\n            }\n            return ({\n                \"HeaderComponent.useEffect\": ()=>window.removeEventListener('mousemove', handleMouseMove)\n            })[\"HeaderComponent.useEffect\"];\n        }\n    }[\"HeaderComponent.useEffect\"], [\n        isMenuOpen\n    ]);\n    const handleMenuClick = ()=>{\n        setIsMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"cuberto-header\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"header-title\",\n                children: \"Frontend Web Design\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"menu-button\",\n                onClick: ()=>setIsMenuOpen(true),\n                children: [\n                    \"menu \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hamburger\",\n                        children: \"≡\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                        lineNumber: 34,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"menu-overlay\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"close-button\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"menu-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"menu-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"menu-heading\",\n                                                children: \"Social media\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://linkedin.com\",\n                                                            target: \"_blank\",\n                                                            rel: \"noreferrer\",\n                                                            children: \"LinkedIn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 45,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://instagram.com\",\n                                                            target: \"_blank\",\n                                                            rel: \"noreferrer\",\n                                                            children: \"Instagram\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://twitter.com\",\n                                                            target: \"_blank\",\n                                                            rel: \"noreferrer\",\n                                                            children: \"Twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://github.com\",\n                                                            target: \"_blank\",\n                                                            rel: \"noreferrer\",\n                                                            children: \"GitHub\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"menu-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"menu-heading\",\n                                                children: \"Menu\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"main-links\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"Home\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 54,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 54,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design/about\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"About Us\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 55,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design/services\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"Services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design/portfolio\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"Portfolio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 57,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design/pricing\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"Pricing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design/faq\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"FAQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design/client-portal\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"Client Portal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/frontend-web-design-contact-us\",\n                                                            onClick: handleMenuClick,\n                                                            children: \"Contact Us\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"contact-block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Get in touch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cursor-circle\",\n                        style: cursorStyle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\FrontendWebDesign\\\\HeaderComponent\\\\HeaderComponent.js\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Gcm9udGVuZFdlYkRlc2lnbi9IZWFkZXJDb21wb25lbnQvSGVhZGVyQ29tcG9uZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDNEM7QUFDYjtBQUVoQixTQUFTRTtJQUN0QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0osK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDSyxhQUFhQyxlQUFlLEdBQUdOLCtDQUFRQSxDQUFDLENBQUM7SUFFaEQsTUFBTU8sa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUksQ0FBQ0wsWUFBWTtRQUNqQkcsZUFBZTtZQUNiRyxNQUFNLEdBQUdELEVBQUVFLE9BQU8sR0FBRyxHQUFHLEVBQUUsQ0FBQztZQUMzQkMsS0FBSyxHQUFHSCxFQUFFSSxPQUFPLEdBQUcsR0FBRyxFQUFFLENBQUM7UUFDNUI7SUFDRjtJQUVBWCxnREFBU0E7cUNBQUM7WUFDUixJQUFJRSxZQUFZO2dCQUNkVSxPQUFPQyxnQkFBZ0IsQ0FBQyxhQUFhUDtZQUN2QyxPQUFPO2dCQUNMTSxPQUFPRSxtQkFBbUIsQ0FBQyxhQUFhUjtZQUMxQztZQUNBOzZDQUFPLElBQU1NLE9BQU9FLG1CQUFtQixDQUFDLGFBQWFSOztRQUN2RDtvQ0FBRztRQUFDSjtLQUFXO0lBRWYsTUFBTWEsa0JBQWtCO1FBQ3RCWixjQUFjO0lBQ2hCO0lBRUEscUJBQ0UsOERBQUNhO1FBQU9DLFdBQVU7OzBCQUNoQiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQWU7Ozs7OzswQkFDOUIsOERBQUNFO2dCQUFPRixXQUFVO2dCQUFjRyxTQUFTLElBQU1qQixjQUFjOztvQkFBTztrQ0FDN0QsOERBQUNrQjt3QkFBS0osV0FBVTtrQ0FBWTs7Ozs7Ozs7Ozs7O1lBR2xDZiw0QkFDQzs7a0NBQ0UsOERBQUNnQjt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFPRixXQUFVO2dDQUFlRyxTQUFTLElBQU1qQixjQUFjOzBDQUFROzs7Ozs7MENBQ3RFLDhEQUFDZTtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0s7Z0RBQUVMLFdBQVU7MERBQWU7Ozs7OzswREFDNUIsOERBQUNNOztrRUFDQyw4REFBQ0M7a0VBQUcsNEVBQUNDOzREQUFFQyxNQUFLOzREQUF1QkMsUUFBTzs0REFBU0MsS0FBSTtzRUFBYTs7Ozs7Ozs7Ozs7a0VBQ3BFLDhEQUFDSjtrRUFBRyw0RUFBQ0M7NERBQUVDLE1BQUs7NERBQXdCQyxRQUFPOzREQUFTQyxLQUFJO3NFQUFhOzs7Ozs7Ozs7OztrRUFDckUsOERBQUNKO2tFQUFHLDRFQUFDQzs0REFBRUMsTUFBSzs0REFBc0JDLFFBQU87NERBQVNDLEtBQUk7c0VBQWE7Ozs7Ozs7Ozs7O2tFQUNuRSw4REFBQ0o7a0VBQUcsNEVBQUNDOzREQUFFQyxNQUFLOzREQUFxQkMsUUFBTzs0REFBU0MsS0FBSTtzRUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3RFLDhEQUFDVjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNLO2dEQUFFTCxXQUFVOzBEQUFlOzs7Ozs7MERBQzVCLDhEQUFDTTtnREFBR04sV0FBVTs7a0VBQ1osOERBQUNPO2tFQUFHLDRFQUFDQzs0REFBRUMsTUFBSzs0REFBdUJOLFNBQVNMO3NFQUFpQjs7Ozs7Ozs7Ozs7a0VBQzdELDhEQUFDUztrRUFBRyw0RUFBQ0M7NERBQUVDLE1BQUs7NERBQTZCTixTQUFTTDtzRUFBaUI7Ozs7Ozs7Ozs7O2tFQUNuRSw4REFBQ1M7a0VBQUcsNEVBQUNDOzREQUFFQyxNQUFLOzREQUFnQ04sU0FBU0w7c0VBQWlCOzs7Ozs7Ozs7OztrRUFDdEUsOERBQUNTO2tFQUFHLDRFQUFDQzs0REFBRUMsTUFBSzs0REFBaUNOLFNBQVNMO3NFQUFpQjs7Ozs7Ozs7Ozs7a0VBQ3ZFLDhEQUFDUztrRUFBRyw0RUFBQ0M7NERBQUVDLE1BQUs7NERBQStCTixTQUFTTDtzRUFBaUI7Ozs7Ozs7Ozs7O2tFQUNyRSw4REFBQ1M7a0VBQUcsNEVBQUNDOzREQUFFQyxNQUFLOzREQUEyQk4sU0FBU0w7c0VBQWlCOzs7Ozs7Ozs7OztrRUFDakUsOERBQUNTO2tFQUFHLDRFQUFDQzs0REFBRUMsTUFBSzs0REFBcUNOLFNBQVNMO3NFQUFpQjs7Ozs7Ozs7Ozs7a0VBQzNFLDhEQUFDUztrRUFBRyw0RUFBQ0M7NERBQUVDLE1BQUs7NERBQWtDTixTQUFTTDtzRUFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUUxRSw4REFBQ0c7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDSztrRUFBRTs7Ozs7O2tFQUNILDhEQUFDRzt3REFBRUMsTUFBSztrRUFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLOUMsOERBQUNSO3dCQUFJRCxXQUFVO3dCQUFnQlksT0FBT3pCOzs7Ozs7Ozs7Ozs7OztBQUtoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhYmNcXERlc2t0b3BcXGJhbHRhclxcYmFsdGFyLWluY1xcYXBwc1xcYXBwXFxjb21wb25lbnRzXFxGcm9udGVuZFdlYkRlc2lnblxcSGVhZGVyQ29tcG9uZW50XFxIZWFkZXJDb21wb25lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgJy4vSGVhZGVyQ29tcG9uZW50LmNzcyc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIZWFkZXJDb21wb25lbnQoKSB7XHJcbiAgY29uc3QgW2lzTWVudU9wZW4sIHNldElzTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjdXJzb3JTdHlsZSwgc2V0Q3Vyc29yU3R5bGVdID0gdXNlU3RhdGUoe30pO1xyXG5cclxuICBjb25zdCBoYW5kbGVNb3VzZU1vdmUgPSAoZSkgPT4ge1xyXG4gICAgaWYgKCFpc01lbnVPcGVuKSByZXR1cm47XHJcbiAgICBzZXRDdXJzb3JTdHlsZSh7XHJcbiAgICAgIGxlZnQ6IGAke2UuY2xpZW50WCAtIDI1fXB4YCxcclxuICAgICAgdG9wOiBgJHtlLmNsaWVudFkgLSAyNX1weGAsXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzTWVudU9wZW4pIHtcclxuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcclxuICAgIH1cclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgaGFuZGxlTW91c2VNb3ZlKTtcclxuICB9LCBbaXNNZW51T3Blbl0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVNZW51Q2xpY2sgPSAoKSA9PiB7XHJcbiAgICBzZXRJc01lbnVPcGVuKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGhlYWRlciBjbGFzc05hbWU9XCJjdWJlcnRvLWhlYWRlclwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImhlYWRlci10aXRsZVwiPkZyb250ZW5kIFdlYiBEZXNpZ248L2Rpdj5cclxuICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJtZW51LWJ1dHRvblwiIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4odHJ1ZSl9PlxyXG4gICAgICAgIG1lbnUgPHNwYW4gY2xhc3NOYW1lPVwiaGFtYnVyZ2VyXCI+4omhPC9zcGFuPlxyXG4gICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgIHtpc01lbnVPcGVuICYmIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZW51LW92ZXJsYXlcIj5cclxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJjbG9zZS1idXR0b25cIiBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX0+w5c8L2J1dHRvbj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZW51LWNvbnRlbnRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1lbnUtbGVmdFwiPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWVudS1oZWFkaW5nXCI+U29jaWFsIG1lZGlhPC9wPlxyXG4gICAgICAgICAgICAgICAgPHVsPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cImh0dHBzOi8vbGlua2VkaW4uY29tXCIgdGFyZ2V0PVwiX2JsYW5rXCIgcmVsPVwibm9yZWZlcnJlclwiPkxpbmtlZEluPC9hPjwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiaHR0cHM6Ly9pbnN0YWdyYW0uY29tXCIgdGFyZ2V0PVwiX2JsYW5rXCIgcmVsPVwibm9yZWZlcnJlclwiPkluc3RhZ3JhbTwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cImh0dHBzOi8vdHdpdHRlci5jb21cIiB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub3JlZmVycmVyXCI+VHdpdHRlcjwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cImh0dHBzOi8vZ2l0aHViLmNvbVwiIHRhcmdldD1cIl9ibGFua1wiIHJlbD1cIm5vcmVmZXJyZXJcIj5HaXRIdWI8L2E+PC9saT5cclxuICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZW51LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtZW51LWhlYWRpbmdcIj5NZW51PC9wPlxyXG4gICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm1haW4tbGlua3NcIj5cclxuICAgICAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvZnJvbnRlbmQtd2ViLWRlc2lnblwiIG9uQ2xpY2s9e2hhbmRsZU1lbnVDbGlja30+SG9tZTwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9mcm9udGVuZC13ZWItZGVzaWduL2Fib3V0XCIgb25DbGljaz17aGFuZGxlTWVudUNsaWNrfT5BYm91dCBVczwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9mcm9udGVuZC13ZWItZGVzaWduL3NlcnZpY2VzXCIgb25DbGljaz17aGFuZGxlTWVudUNsaWNrfT5TZXJ2aWNlczwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9mcm9udGVuZC13ZWItZGVzaWduL3BvcnRmb2xpb1wiIG9uQ2xpY2s9e2hhbmRsZU1lbnVDbGlja30+UG9ydGZvbGlvPC9hPjwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL2Zyb250ZW5kLXdlYi1kZXNpZ24vcHJpY2luZ1wiIG9uQ2xpY2s9e2hhbmRsZU1lbnVDbGlja30+UHJpY2luZzwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9mcm9udGVuZC13ZWItZGVzaWduL2ZhcVwiIG9uQ2xpY2s9e2hhbmRsZU1lbnVDbGlja30+RkFRPC9hPjwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL2Zyb250ZW5kLXdlYi1kZXNpZ24vY2xpZW50LXBvcnRhbFwiIG9uQ2xpY2s9e2hhbmRsZU1lbnVDbGlja30+Q2xpZW50IFBvcnRhbDwvYT48L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9mcm9udGVuZC13ZWItZGVzaWduLWNvbnRhY3QtdXNcIiBvbkNsaWNrPXtoYW5kbGVNZW51Q2xpY2t9PkNvbnRhY3QgVXM8L2E+PC9saT5cclxuICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhY3QtYmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgPHA+R2V0IGluIHRvdWNoPC9wPlxyXG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwibWFpbHRvOmdldGludG91Y2hAZ21haWwuY29tXCI+Z2V0aW50b3VjaEBnbWFpbC5jb208L2E+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY3Vyc29yLWNpcmNsZVwiIHN0eWxlPXtjdXJzb3JTdHlsZX0+PC9kaXY+XHJcbiAgICAgICAgPC8+XHJcbiAgICAgICl9XHJcbiAgICA8L2hlYWRlcj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkhlYWRlckNvbXBvbmVudCIsImlzTWVudU9wZW4iLCJzZXRJc01lbnVPcGVuIiwiY3Vyc29yU3R5bGUiLCJzZXRDdXJzb3JTdHlsZSIsImhhbmRsZU1vdXNlTW92ZSIsImUiLCJsZWZ0IiwiY2xpZW50WCIsInRvcCIsImNsaWVudFkiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZU1lbnVDbGljayIsImhlYWRlciIsImNsYXNzTmFtZSIsImRpdiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIiwicCIsInVsIiwibGkiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsInN0eWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.js\n");

/***/ }),

/***/ "(ssr)/./app/components/MetaStyleComponents/MetaFooter.js":
/*!**********************************************************!*\
  !*** ./app/components/MetaStyleComponents/MetaFooter.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MetaFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MetaFooter.module.css */ \"(ssr)/./app/components/MetaStyleComponents/MetaFooter.module.css\");\n/* harmony import */ var _MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst footerSections = {\n    'Technologies': [\n        {\n            name: 'Frontend Web Design',\n            href: '/frontend-web-design',\n            external: true\n        },\n        {\n            name: 'Cre8ive Studio',\n            href: '/cre8ive-studio-comingsoon',\n            external: false\n        },\n        {\n            name: 'Transac',\n            href: '/transac',\n            external: true\n        }\n    ],\n    'Services': [\n        {\n            name: 'Hospitality',\n            href: '/sip-and-savour',\n            external: true\n        },\n        {\n            name: 'Engineering',\n            href: '/archon-engineering-comingsoon',\n            external: false\n        },\n        {\n            name: 'Finance',\n            href: '/baltar-finance-comingsoon',\n            external: false\n        }\n    ],\n    'Brands': [\n        {\n            name: 'VR Fashion',\n            href: '/vr',\n            external: true\n        },\n        {\n            name: 'Le Mode Co.',\n            href: '/le-mode-co',\n            external: true\n        },\n        {\n            name: 'Consumer Pulse',\n            href: '/consumer-pulse',\n            external: true\n        }\n    ],\n    'Company': [\n        {\n            name: 'About',\n            href: '/about-comingsoon',\n            external: false\n        },\n        {\n            name: 'Careers',\n            href: '/careers-comingsoon',\n            external: false\n        },\n        {\n            name: 'Contact',\n            href: '/contact-us',\n            external: false\n        }\n    ]\n};\nfunction MetaFooter() {\n    const currentYear = new Date().getFullYear();\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().footer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().content),\n                variants: containerVariants,\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true,\n                    threshold: 0.1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().mainContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().companySection),\n                                variants: itemVariants,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().companyName),\n                                        children: \"Baltar Inc\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().companyTagline),\n                                        children: \"One Company. Limitless Services.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().companyDescription),\n                                        children: \"A multi-division firm offering advanced solutions across construction, technology, hospitality, finance, fashion, and media.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().linksGrid),\n                                children: Object.entries(footerSections).map(([section, links])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().linkSection),\n                                        variants: itemVariants,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionTitle),\n                                                children: section\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().linkList),\n                                                children: links.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: link.external ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: link.href,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLink),\n                                                            children: link.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().footerLink),\n                                                            children: link.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, section, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().bottomBar),\n                        variants: itemVariants,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().bottomContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().copyright),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" Baltar Inc. All rights reserved.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().legalLinks),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/privacy-policy\",\n                                            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().legalLink),\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/terms-of-service\",\n                                            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().legalLink),\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/cookies\",\n                                            className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().legalLink),\n                                            children: \"Cookies\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_MetaFooter_module_css__WEBPACK_IMPORTED_MODULE_2___default().location),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83C\\uDDE8\\uD83C\\uDDE6 Canada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaFooter.js\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/MetaStyleComponents/MetaFooter.js\n");

/***/ }),

/***/ "(ssr)/./app/components/MetaStyleComponents/MetaFooter.module.css":
/*!******************************************************************!*\
  !*** ./app/components/MetaStyleComponents/MetaFooter.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"footer\": \"MetaFooter_footer__pVpDf\",\n\t\"container\": \"MetaFooter_container__4Ma_Z\",\n\t\"content\": \"MetaFooter_content__fvTia\",\n\t\"mainContent\": \"MetaFooter_mainContent__njUct\",\n\t\"companySection\": \"MetaFooter_companySection__1f2QK\",\n\t\"companyName\": \"MetaFooter_companyName__WPiYO\",\n\t\"companyTagline\": \"MetaFooter_companyTagline__EAUBO\",\n\t\"companyDescription\": \"MetaFooter_companyDescription__i6tOx\",\n\t\"linksGrid\": \"MetaFooter_linksGrid__30I6m\",\n\t\"linkSection\": \"MetaFooter_linkSection__Isx5i\",\n\t\"sectionTitle\": \"MetaFooter_sectionTitle__BqHrQ\",\n\t\"linkList\": \"MetaFooter_linkList__G3qsi\",\n\t\"footerLink\": \"MetaFooter_footerLink__k05NU\",\n\t\"bottomBar\": \"MetaFooter_bottomBar__RXjOx\",\n\t\"bottomContent\": \"MetaFooter_bottomContent__WwmiF\",\n\t\"copyright\": \"MetaFooter_copyright__bQ7lL\",\n\t\"legalLinks\": \"MetaFooter_legalLinks__N_6HR\",\n\t\"legalLink\": \"MetaFooter_legalLink__2V29R\",\n\t\"location\": \"MetaFooter_location__RsY7n\"\n};\n\nmodule.exports.__checksum = \"e50597c91803\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9NZXRhU3R5bGVDb21wb25lbnRzL01ldGFGb290ZXIubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWJjXFxEZXNrdG9wXFxiYWx0YXJcXGJhbHRhci1pbmNcXGFwcHNcXGFwcFxcY29tcG9uZW50c1xcTWV0YVN0eWxlQ29tcG9uZW50c1xcTWV0YUZvb3Rlci5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImZvb3RlclwiOiBcIk1ldGFGb290ZXJfZm9vdGVyX19wVnBEZlwiLFxuXHRcImNvbnRhaW5lclwiOiBcIk1ldGFGb290ZXJfY29udGFpbmVyX180TWFfWlwiLFxuXHRcImNvbnRlbnRcIjogXCJNZXRhRm9vdGVyX2NvbnRlbnRfX2Z2VGlhXCIsXG5cdFwibWFpbkNvbnRlbnRcIjogXCJNZXRhRm9vdGVyX21haW5Db250ZW50X19ualVjdFwiLFxuXHRcImNvbXBhbnlTZWN0aW9uXCI6IFwiTWV0YUZvb3Rlcl9jb21wYW55U2VjdGlvbl9fMWYyUUtcIixcblx0XCJjb21wYW55TmFtZVwiOiBcIk1ldGFGb290ZXJfY29tcGFueU5hbWVfX1dQaVlPXCIsXG5cdFwiY29tcGFueVRhZ2xpbmVcIjogXCJNZXRhRm9vdGVyX2NvbXBhbnlUYWdsaW5lX19FQVVCT1wiLFxuXHRcImNvbXBhbnlEZXNjcmlwdGlvblwiOiBcIk1ldGFGb290ZXJfY29tcGFueURlc2NyaXB0aW9uX19pNnRPeFwiLFxuXHRcImxpbmtzR3JpZFwiOiBcIk1ldGFGb290ZXJfbGlua3NHcmlkX18zMEk2bVwiLFxuXHRcImxpbmtTZWN0aW9uXCI6IFwiTWV0YUZvb3Rlcl9saW5rU2VjdGlvbl9fSXN4NWlcIixcblx0XCJzZWN0aW9uVGl0bGVcIjogXCJNZXRhRm9vdGVyX3NlY3Rpb25UaXRsZV9fQnFIclFcIixcblx0XCJsaW5rTGlzdFwiOiBcIk1ldGFGb290ZXJfbGlua0xpc3RfX0czcXNpXCIsXG5cdFwiZm9vdGVyTGlua1wiOiBcIk1ldGFGb290ZXJfZm9vdGVyTGlua19fazA1TlVcIixcblx0XCJib3R0b21CYXJcIjogXCJNZXRhRm9vdGVyX2JvdHRvbUJhcl9fUlhqT3hcIixcblx0XCJib3R0b21Db250ZW50XCI6IFwiTWV0YUZvb3Rlcl9ib3R0b21Db250ZW50X19Xd21pRlwiLFxuXHRcImNvcHlyaWdodFwiOiBcIk1ldGFGb290ZXJfY29weXJpZ2h0X19iUTdsTFwiLFxuXHRcImxlZ2FsTGlua3NcIjogXCJNZXRhRm9vdGVyX2xlZ2FsTGlua3NfX05fNkhSXCIsXG5cdFwibGVnYWxMaW5rXCI6IFwiTWV0YUZvb3Rlcl9sZWdhbExpbmtfXzJWMjlSXCIsXG5cdFwibG9jYXRpb25cIjogXCJNZXRhRm9vdGVyX2xvY2F0aW9uX19Sc1k3blwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlNTA1OTdjOTE4MDNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/MetaStyleComponents/MetaFooter.module.css\n");

/***/ }),

/***/ "(ssr)/./app/components/MetaStyleComponents/MetaHeader.js":
/*!**********************************************************!*\
  !*** ./app/components/MetaStyleComponents/MetaHeader.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MetaHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MetaHeader.module.css */ \"(ssr)/./app/components/MetaStyleComponents/MetaHeader.module.css\");\n/* harmony import */ var _MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navItems = {\n    Technologies: [\n        'Frontend Web Design',\n        'Cre8ive Studio',\n        'Transac'\n    ],\n    Hospitality: [\n        'Savour & Sip'\n    ],\n    Engineering: [\n        'Archon Engineering'\n    ],\n    Finance: [\n        'Baltar Wealth Management'\n    ],\n    Fashion: [\n        'VR (Luxury Eyewear & Fashion Tech)',\n        'Le Mode Co.'\n    ],\n    Media: [\n        'Consumer Pulse',\n        'Zeitgeist Media'\n    ]\n};\nconst hrefMap = {\n    'transac': '/transac',\n    'frontend web design': '/frontend-web-design',\n    'le mode co.': '/le-mode-co',\n    'savour & sip': '/sip-and-savour',\n    'consumer pulse': '/consumer-pulse',\n    'vr (luxury eyewear & fashion tech)': '/vr',\n    'cre8ive studio': '/cre8ive-studio-comingsoon',\n    'archon engineering': '/archon-engineering-comingsoon',\n    'baltar wealth management': '/baltar-finance-comingsoon',\n    'zeitgeist media': '/zeitgeist-media-comingsoon'\n};\nfunction MetaHeader() {\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MetaHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"MetaHeader.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 20);\n                }\n            }[\"MetaHeader.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"MetaHeader.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"MetaHeader.useEffect\"];\n        }\n    }[\"MetaHeader.useEffect\"], []);\n    const handleMouseEnter = (key)=>{\n        clearTimeout(timeoutRef.current);\n        setActiveDropdown(key);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setActiveDropdown(null);\n        }, 150);\n    };\n    const renderLink = (item, i)=>{\n        const lowerItem = item.toLowerCase();\n        const href = hrefMap[lowerItem] || '/coming-soon';\n        const isIntegratedPage = [\n            'transac',\n            'frontend web design',\n            'savour & sip',\n            'le mode co.',\n            'consumer pulse',\n            'vr (luxury eyewear & fashion tech)'\n        ].includes(lowerItem);\n        if (isIntegratedPage) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: href,\n                className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: item\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: href,\n                className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                children: item\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.header, {\n        className: `${(_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().header)} ${scrolled ? (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().scrolled) : ''}`,\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().container),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: \"/logo.svg\",\n                                alt: \"Baltar Inc\",\n                                width: 32,\n                                height: 32\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoText),\n                                children: \"Baltar Inc\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().nav),\n                        children: Object.entries(navItems).map(([category, items])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navItem),\n                                onMouseEnter: ()=>handleMouseEnter(category),\n                                onMouseLeave: handleMouseLeave,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navLink),\n                                        children: category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                        children: activeDropdown === category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdown),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            children: items.map((item, i)=>renderLink(item, i))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, category, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileMenuButton),\n                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `${(_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().hamburgerLine)} ${mobileMenuOpen ? (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : ''}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `${(_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().hamburgerLine)} ${mobileMenuOpen ? (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : ''}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `${(_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().hamburgerLine)} ${mobileMenuOpen ? (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : ''}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileMenu),\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: 'auto'\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: Object.entries(navItems).map(([category, items])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileCategory),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileCategoryTitle),\n                                    children: category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_MetaHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileItems),\n                                    children: items.map((item, i)=>renderLink(item, i))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, category, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                            lineNumber: 145,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\MetaStyleComponents\\\\MetaHeader.js\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/MetaStyleComponents/MetaHeader.js\n");

/***/ }),

/***/ "(ssr)/./app/components/MetaStyleComponents/MetaHeader.module.css":
/*!******************************************************************!*\
  !*** ./app/components/MetaStyleComponents/MetaHeader.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"MetaHeader_header__ZbNqr\",\n\t\"scrolled\": \"MetaHeader_scrolled__EANpU\",\n\t\"container\": \"MetaHeader_container__xK6T0\",\n\t\"logo\": \"MetaHeader_logo__NmYEZ\",\n\t\"logoText\": \"MetaHeader_logoText__92n0c\",\n\t\"nav\": \"MetaHeader_nav__hJ1j7\",\n\t\"navItem\": \"MetaHeader_navItem__auUlc\",\n\t\"navLink\": \"MetaHeader_navLink__JXTOX\",\n\t\"dropdown\": \"MetaHeader_dropdown__X1T3X\",\n\t\"dropdownItem\": \"MetaHeader_dropdownItem__ApPdf\",\n\t\"mobileMenuButton\": \"MetaHeader_mobileMenuButton__Egs_n\",\n\t\"hamburgerLine\": \"MetaHeader_hamburgerLine__FCwvU\",\n\t\"active\": \"MetaHeader_active__7Fexx\",\n\t\"mobileMenu\": \"MetaHeader_mobileMenu__Kc_00\",\n\t\"mobileCategory\": \"MetaHeader_mobileCategory__aQOf6\",\n\t\"mobileCategoryTitle\": \"MetaHeader_mobileCategoryTitle__ghVdJ\",\n\t\"mobileItems\": \"MetaHeader_mobileItems__MgXLv\"\n};\n\nmodule.exports.__checksum = \"be8fd22b2ea8\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9NZXRhU3R5bGVDb21wb25lbnRzL01ldGFIZWFkZXIubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhYmNcXERlc2t0b3BcXGJhbHRhclxcYmFsdGFyLWluY1xcYXBwc1xcYXBwXFxjb21wb25lbnRzXFxNZXRhU3R5bGVDb21wb25lbnRzXFxNZXRhSGVhZGVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiaGVhZGVyXCI6IFwiTWV0YUhlYWRlcl9oZWFkZXJfX1piTnFyXCIsXG5cdFwic2Nyb2xsZWRcIjogXCJNZXRhSGVhZGVyX3Njcm9sbGVkX19FQU5wVVwiLFxuXHRcImNvbnRhaW5lclwiOiBcIk1ldGFIZWFkZXJfY29udGFpbmVyX194SzZUMFwiLFxuXHRcImxvZ29cIjogXCJNZXRhSGVhZGVyX2xvZ29fX05tWUVaXCIsXG5cdFwibG9nb1RleHRcIjogXCJNZXRhSGVhZGVyX2xvZ29UZXh0X185Mm4wY1wiLFxuXHRcIm5hdlwiOiBcIk1ldGFIZWFkZXJfbmF2X19oSjFqN1wiLFxuXHRcIm5hdkl0ZW1cIjogXCJNZXRhSGVhZGVyX25hdkl0ZW1fX2F1VWxjXCIsXG5cdFwibmF2TGlua1wiOiBcIk1ldGFIZWFkZXJfbmF2TGlua19fSlhUT1hcIixcblx0XCJkcm9wZG93blwiOiBcIk1ldGFIZWFkZXJfZHJvcGRvd25fX1gxVDNYXCIsXG5cdFwiZHJvcGRvd25JdGVtXCI6IFwiTWV0YUhlYWRlcl9kcm9wZG93bkl0ZW1fX0FwUGRmXCIsXG5cdFwibW9iaWxlTWVudUJ1dHRvblwiOiBcIk1ldGFIZWFkZXJfbW9iaWxlTWVudUJ1dHRvbl9fRWdzX25cIixcblx0XCJoYW1idXJnZXJMaW5lXCI6IFwiTWV0YUhlYWRlcl9oYW1idXJnZXJMaW5lX19GQ3d2VVwiLFxuXHRcImFjdGl2ZVwiOiBcIk1ldGFIZWFkZXJfYWN0aXZlX183RmV4eFwiLFxuXHRcIm1vYmlsZU1lbnVcIjogXCJNZXRhSGVhZGVyX21vYmlsZU1lbnVfX0tjXzAwXCIsXG5cdFwibW9iaWxlQ2F0ZWdvcnlcIjogXCJNZXRhSGVhZGVyX21vYmlsZUNhdGVnb3J5X19hUU9mNlwiLFxuXHRcIm1vYmlsZUNhdGVnb3J5VGl0bGVcIjogXCJNZXRhSGVhZGVyX21vYmlsZUNhdGVnb3J5VGl0bGVfX2doVmRKXCIsXG5cdFwibW9iaWxlSXRlbXNcIjogXCJNZXRhSGVhZGVyX21vYmlsZUl0ZW1zX19NZ1hMdlwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJiZThmZDIyYjJlYThcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/MetaStyleComponents/MetaHeader.module.css\n");

/***/ }),

/***/ "(ssr)/./app/components/NavBarComponent/Navbar.js":
/*!**************************************************!*\
  !*** ./app/components/NavBarComponent/Navbar.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _navbar_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./navbar.css */ \"(ssr)/./app/components/NavBarComponent/navbar.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navItems = {\n    Finance: [\n        'Wealth Management',\n        'Transac'\n    ],\n    Technology: [\n        'Frontend Web Design',\n        'Cre8ive Studio'\n    ],\n    Consulting: [\n        'Archon Engineering'\n    ],\n    Hospitality: [\n        'Savour & Sip'\n    ],\n    Fashion: [\n        'VR (Luxury Eyewear & Fashion Tech)',\n        'Le Mode Co.'\n    ],\n    Media: [\n        'Consumer Pulse',\n        'Zeitgeist Media'\n    ],\n    Retail: [\n        'Baltar Prime'\n    ]\n};\nconst hrefMap = {\n    'transac': '/transac',\n    'frontend web design': '/frontend-web-design',\n    'le mode co.': '/le-mode-co',\n    'savour & sip': '/sip-and-savour',\n    'consumer pulse': '/consumer-pulse',\n    'vr (luxury eyewear & fashion tech)': '/vr'\n};\nfunction Navbar() {\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mobileOpen, setMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDropdownOpen, setMobileDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseEnter = (key)=>{\n        clearTimeout(timeoutRef.current);\n        setActive(key);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setActive(null);\n        }, 150);\n    };\n    const toggleMobileDropdown = (key)=>{\n        if (mobileDropdownOpen === key) {\n            setMobileDropdownOpen(null);\n        } else {\n            setMobileDropdownOpen(key);\n        }\n    };\n    const renderLink = (item, i)=>{\n        const lowerItem = item.toLowerCase();\n        const href = hrefMap[lowerItem] || '/coming-soon';\n        // Integrated pages should open in new tab, coming soon pages stay in same tab\n        const isIntegratedPage = hrefMap[lowerItem] && href !== '/coming-soon';\n        if (isIntegratedPage) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: href,\n                className: \"dropdown-item\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: item\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: href,\n                className: \"dropdown-item\",\n                children: item\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"navbar\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"navbar-content\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"logo-with-icon\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/logo.svg\",\n                            alt: \"Baltar Inc Logo\",\n                            width: 28,\n                            height: 28\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"logo-text\",\n                            children: \"Baltar Inc\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"hamburger\",\n                    onClick: ()=>setMobileOpen(true),\n                    children: \"☰\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"nav-links\",\n                    children: Object.entries(navItems).map(([heading, subItems], idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav-item-wrapper\",\n                            onMouseEnter: ()=>handleMouseEnter(heading),\n                            onMouseLeave: handleMouseLeave,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"nav-item\",\n                                    children: heading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                active === heading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"dropdown\",\n                                    children: subItems.map((item, i)=>renderLink(item, i))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, idx, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                mobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mobile-menu\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"close-btn\",\n                            onClick: ()=>setMobileOpen(false),\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        Object.entries(navItems).map(([heading, subItems], idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mobile-dropdown-wrapper\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"mobile-dropdown-toggle\",\n                                        onClick: ()=>toggleMobileDropdown(heading),\n                                        children: heading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    mobileDropdownOpen === heading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-dropdown\",\n                                        children: subItems.map((item, i)=>{\n                                            const lowerItem = item.toLowerCase();\n                                            const href = hrefMap[lowerItem] || '/coming-soon';\n                                            const isIntegratedPage = hrefMap[lowerItem] && href !== '/coming-soon';\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>!isIntegratedPage && setMobileOpen(false),\n                                                children: renderLink(item, i)\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                                                lineNumber: 134,\n                                                columnNumber: 25\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, idx, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\NavBarComponent\\\\Navbar.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NavBarComponent/Navbar.js\n");

/***/ }),

/***/ "(ssr)/./app/components/NavBarComponent/navbar.css":
/*!***************************************************!*\
  !*** ./app/components/NavBarComponent/navbar.css ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5a5e61e5a0c0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9OYXZCYXJDb21wb25lbnQvbmF2YmFyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhYmNcXERlc2t0b3BcXGJhbHRhclxcYmFsdGFyLWluY1xcYXBwc1xcYXBwXFxjb21wb25lbnRzXFxOYXZCYXJDb21wb25lbnRcXG5hdmJhci5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1YTVlNjFlNWEwYzBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NavBarComponent/navbar.css\n");

/***/ }),

/***/ "(ssr)/./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.js":
/*!*****************************************************************************!*\
  !*** ./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageLoader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _PageAnimation_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PageAnimation.module.css */ \"(ssr)/./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.module.css\");\n/* harmony import */ var _PageAnimation_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_PageAnimation_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PageLoader() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageLoader.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"PageLoader.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                }\n            }[\"PageLoader.useEffect.timer\"], 2500); // 2.5 seconds\n            return ({\n                \"PageLoader.useEffect\": ()=>clearTimeout(timer)\n            })[\"PageLoader.useEffect\"];\n        }\n    }[\"PageLoader.useEffect\"], []);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_PageAnimation_module_css__WEBPACK_IMPORTED_MODULE_2___default().loader),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            children: \"SAVOUR & SIP\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\SavourAndSip\\\\PageAnimationComponent\\\\PageAnimation.js\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\SavourAndSip\\\\PageAnimationComponent\\\\PageAnimation.js\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TYXZvdXJBbmRTaXAvUGFnZUFuaW1hdGlvbkNvbXBvbmVudC9QYWdlQW5pbWF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0k7QUFFakMsU0FBU0c7SUFDdEIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdKLCtDQUFRQSxDQUFDO0lBRTNDRCxnREFBU0E7Z0NBQUM7WUFDUixNQUFNTSxRQUFRQzs4Q0FBVztvQkFDdkJGLGFBQWE7Z0JBQ2Y7NkNBQUcsT0FBTyxjQUFjO1lBRXhCO3dDQUFPLElBQU1HLGFBQWFGOztRQUM1QjsrQkFBRyxFQUFFO0lBRUwsSUFBSSxDQUFDRixXQUFXLE9BQU87SUFFdkIscUJBQ0UsOERBQUNLO1FBQUlDLFdBQVdSLHlFQUFhO2tCQUMzQiw0RUFBQ1U7c0JBQUc7Ozs7Ozs7Ozs7O0FBR1YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWJjXFxEZXNrdG9wXFxiYWx0YXJcXGJhbHRhci1pbmNcXGFwcHNcXGFwcFxcY29tcG9uZW50c1xcU2F2b3VyQW5kU2lwXFxQYWdlQW5pbWF0aW9uQ29tcG9uZW50XFxQYWdlQW5pbWF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgc3R5bGVzIGZyb20gXCIuL1BhZ2VBbmltYXRpb24ubW9kdWxlLmNzc1wiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZUxvYWRlcigpIHtcclxuICBjb25zdCBbaXNWaXNpYmxlLCBzZXRJc1Zpc2libGVdID0gdXNlU3RhdGUodHJ1ZSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBzZXRJc1Zpc2libGUoZmFsc2UpO1xyXG4gICAgfSwgMjUwMCk7IC8vIDIuNSBzZWNvbmRzXHJcblxyXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XHJcbiAgfSwgW10pO1xyXG5cclxuICBpZiAoIWlzVmlzaWJsZSkgcmV0dXJuIG51bGw7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmxvYWRlcn0+XHJcbiAgICAgIDxoMT5TQVZPVVIgJiBTSVA8L2gxPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJzdHlsZXMiLCJQYWdlTG9hZGVyIiwiaXNWaXNpYmxlIiwic2V0SXNWaXNpYmxlIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZGl2IiwiY2xhc3NOYW1lIiwibG9hZGVyIiwiaDEiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.js\n");

/***/ }),

/***/ "(ssr)/./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.module.css":
/*!*************************************************************************************!*\
  !*** ./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.module.css ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"loader\": \"PageAnimation_loader__b8dp4\",\n\t\"fadeOut\": \"PageAnimation_fadeOut__0qT_d\",\n\t\"fadeIn\": \"PageAnimation_fadeIn__brgQZ\"\n};\n\nmodule.exports.__checksum = \"857bba8d4ebe\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TYXZvdXJBbmRTaXAvUGFnZUFuaW1hdGlvbkNvbXBvbmVudC9QYWdlQW5pbWF0aW9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhYmNcXERlc2t0b3BcXGJhbHRhclxcYmFsdGFyLWluY1xcYXBwc1xcYXBwXFxjb21wb25lbnRzXFxTYXZvdXJBbmRTaXBcXFBhZ2VBbmltYXRpb25Db21wb25lbnRcXFBhZ2VBbmltYXRpb24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJsb2FkZXJcIjogXCJQYWdlQW5pbWF0aW9uX2xvYWRlcl9fYjhkcDRcIixcblx0XCJmYWRlT3V0XCI6IFwiUGFnZUFuaW1hdGlvbl9mYWRlT3V0X18wcVRfZFwiLFxuXHRcImZhZGVJblwiOiBcIlBhZ2VBbmltYXRpb25fZmFkZUluX19icmdRWlwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI4NTdiYmE4ZDRlYmVcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.module.css\n");

/***/ }),

/***/ "(ssr)/./app/components/Transac/HeaderComponent/TransacHeader.js":
/*!*****************************************************************!*\
  !*** ./app/components/Transac/HeaderComponent/TransacHeader.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransacHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TransacHeader.module.css */ \"(ssr)/./app/components/Transac/HeaderComponent/TransacHeader.module.css\");\n/* harmony import */ var _TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navItems = [\n    {\n        label: 'Why Transac',\n        href: '#why'\n    },\n    {\n        label: 'Demo',\n        href: '#demo'\n    },\n    {\n        label: 'Pricing',\n        href: '#pricing'\n    },\n    {\n        label: 'Testimonials',\n        href: '#testimonials'\n    },\n    {\n        label: 'App',\n        href: '#app'\n    }\n];\nfunction TransacHeader() {\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mobileOpen, setMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResources, setShowResources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseEnter = (item)=>{\n        clearTimeout(timeoutRef.current);\n        setActive(item);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setActive(null);\n        }, 150);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().header),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().container),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/transac\",\n                    className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoWithIcon),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/logo.svg\",\n                            alt: \"Transac Logo\",\n                            width: 28,\n                            height: 28\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoText),\n                            children: \"Transac\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileMenuButton),\n                    onClick: ()=>setMobileOpen(true),\n                    children: \"☰\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navbar),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navLinks),\n                        children: [\n                            navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navItem),\n                                    children: item.label\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onMouseEnter: ()=>handleMouseEnter('resources'),\n                                onMouseLeave: handleMouseLeave,\n                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navItemWrapper),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navItem),\n                                        children: \"Resources\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    active === 'resources' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdown),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/compliance\",\n                                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                                children: \"Compliance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/hardware\",\n                                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                                children: \"Hardware\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/loyalty\",\n                                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                                children: \"Loyalty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/integrations\",\n                                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                                children: \"Integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/signup\",\n                                className: `${(_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navItem)} ${(_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().ctaButton)}`,\n                                children: \"Join Transac\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().signInPrompt),\n                                children: [\n                                    \"Already onboard?\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().signInLink),\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                mobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${(_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileMenu)} ${(_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuSlide)}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setMobileOpen(false),\n                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().closeBtn),\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, this),\n                        navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: item.href,\n                                className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().navItem),\n                                onClick: ()=>setMobileOpen(false),\n                                children: item.label\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileDropdownWrapper),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowResources(!showResources),\n                                    className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileDropdownToggle),\n                                    children: [\n                                        \"Resources \",\n                                        showResources ? '' : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                showResources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobileDropdown),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/compliance\",\n                                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                            children: \"Compliance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/hardware\",\n                                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                            children: \"Hardware\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/loyalty\",\n                                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                            children: \"Loyalty\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/integrations\",\n                                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().dropdownItem),\n                                            children: \"Integrations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/signup\",\n                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().ctaButton),\n                            onClick: ()=>setMobileOpen(false),\n                            children: \"Join Transac\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/login\",\n                            className: (_TransacHeader_module_css__WEBPACK_IMPORTED_MODULE_4___default().signInLink),\n                            onClick: ()=>setMobileOpen(false),\n                            children: \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\Transac\\\\HeaderComponent\\\\TransacHeader.js\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Transac/HeaderComponent/TransacHeader.js\n");

/***/ }),

/***/ "(ssr)/./app/components/Transac/HeaderComponent/TransacHeader.module.css":
/*!*************************************************************************!*\
  !*** ./app/components/Transac/HeaderComponent/TransacHeader.module.css ***!
  \*************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"TransacHeader_header__AjiOP\",\n\t\"container\": \"TransacHeader_container__VZ7am\",\n\t\"logoWithIcon\": \"TransacHeader_logoWithIcon__BfTKB\",\n\t\"logoText\": \"TransacHeader_logoText__uxLYp\",\n\t\"navbar\": \"TransacHeader_navbar__FHJK2\",\n\t\"navLinks\": \"TransacHeader_navLinks__q5D6E\",\n\t\"navItem\": \"TransacHeader_navItem__IoYtz\",\n\t\"navItemWrapper\": \"TransacHeader_navItemWrapper__MmGeO\",\n\t\"dropdown\": \"TransacHeader_dropdown__PwmN4\",\n\t\"dropdownItem\": \"TransacHeader_dropdownItem__V2nwY\",\n\t\"signInPrompt\": \"TransacHeader_signInPrompt__0RdHn\",\n\t\"ctaButton\": \"TransacHeader_ctaButton__EJ9QC\",\n\t\"signInLink\": \"TransacHeader_signInLink__ohniV\",\n\t\"mobileMenuButton\": \"TransacHeader_mobileMenuButton__IYxxr\",\n\t\"mobileMenu\": \"TransacHeader_mobileMenu__Nv6KS\",\n\t\"slideIn\": \"TransacHeader_slideIn__JqSzc\",\n\t\"menuSlide\": \"TransacHeader_menuSlide__5ZUsI\",\n\t\"slideOut\": \"TransacHeader_slideOut__O5DiR\",\n\t\"closeBtn\": \"TransacHeader_closeBtn__rTHVm\",\n\t\"mobileDropdownWrapper\": \"TransacHeader_mobileDropdownWrapper__S3VM7\",\n\t\"mobileDropdownToggle\": \"TransacHeader_mobileDropdownToggle__8NR8e\",\n\t\"mobileDropdown\": \"TransacHeader_mobileDropdown__rMLRI\"\n};\n\nmodule.exports.__checksum = \"b69d26192b33\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Transac/HeaderComponent/TransacHeader.module.css\n");

/***/ }),

/***/ "(ssr)/./app/components/layoutClient.js":
/*!****************************************!*\
  !*** ./app/components/layoutClient.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LayoutClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _NavBarComponent_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NavBarComponent/Navbar */ \"(ssr)/./app/components/NavBarComponent/Navbar.js\");\n/* harmony import */ var _FooterComponent_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FooterComponent/Footer */ \"(ssr)/./app/components/FooterComponent/Footer.js\");\n/* harmony import */ var _MetaStyleComponents_MetaHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MetaStyleComponents/MetaHeader */ \"(ssr)/./app/components/MetaStyleComponents/MetaHeader.js\");\n/* harmony import */ var _MetaStyleComponents_MetaFooter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MetaStyleComponents/MetaFooter */ \"(ssr)/./app/components/MetaStyleComponents/MetaFooter.js\");\n/* harmony import */ var _Transac_HeaderComponent_TransacHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Transac/HeaderComponent/TransacHeader */ \"(ssr)/./app/components/Transac/HeaderComponent/TransacHeader.js\");\n/* harmony import */ var _FrontendWebDesign_HeaderComponent_HeaderComponent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FrontendWebDesign/HeaderComponent/HeaderComponent */ \"(ssr)/./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.js\");\n/* harmony import */ var _SavourAndSip_PageAnimationComponent_PageAnimation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SavourAndSip/PageAnimationComponent/PageAnimation */ \"(ssr)/./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n // ✅ Import loader\nfunction LayoutClient({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const isHome = pathname === '/';\n    const isTransac = pathname === '/transac';\n    const isFrontendDesign = pathname === '/frontend-web-design';\n    const isComingSoonPage = pathname === '/coming-soon';\n    const isAuthPage = pathname === '/signup' || pathname === '/login';\n    const isTransacComingSoonPage = pathname === '/transac-coming-soon';\n    const isFrontendComingSoonPage = pathname === '/frontend-web-design-comingsoon';\n    const isFrontendContactUsPage = pathname === '/frontend-web-design-contact-us';\n    const isLeModeCoPage = pathname === '/le-mode-co';\n    const isLeModeCoComingSoonPage = pathname === '/le-mode-co-comingsoon';\n    const isLeModeCoContactUsPage = pathname === '/le-mode-co-contact-us';\n    const isLeModeCoSubscribePage = pathname === '/le-mode-co-subscribe';\n    const isSavourAndSip = pathname === '/sip-and-savour';\n    const isSavourAndSipComingSoonPage = pathname === '/savour-and-sip-coming-soon';\n    // Savour & Sip pages\n    const isSavourAboutPage = pathname === '/sip-and-savour/about';\n    const isSavourServicesPage = pathname === '/sip-and-savour/services';\n    const isSavourEventsPage = pathname === '/sip-and-savour/events';\n    const isSavourMenuPage = pathname === '/sip-and-savour/menu';\n    const isSavourPricingPage = pathname === '/sip-and-savour/pricing';\n    // Check if any Savour & Sip page\n    const isAnySavourPage = isSavourAndSip || isSavourAboutPage || isSavourServicesPage || isSavourEventsPage || isSavourMenuPage || isSavourPricingPage;\n    const isConsumerPulsepage = pathname === '/consumer-pulse';\n    const isConsumerPulseLoginpage = pathname === '/consumer-pulse-signin';\n    const isConsumerPulseSignuppage = pathname === '/consumer-pulse-signup';\n    const isConsumerPulseComingSoonpage = pathname === '/consumer-pulse-comingsoon';\n    const isConsumerPulseSurveyspage = pathname === '/consumer-pulse-surveys';\n    const isConsumerPulsePollingpage = pathname === '/consumer-pulse-polling';\n    const isConsumerPulseAnalyticspage = pathname === '/consumer-pulse-analytics';\n    // Group all Consumer Pulse pages\n    const isAnyConsumerPulsePage = isConsumerPulsepage || isConsumerPulseLoginpage || isConsumerPulseSignuppage || isConsumerPulseComingSoonpage || isConsumerPulseSurveyspage || isConsumerPulsePollingpage || isConsumerPulseAnalyticspage;\n    const isVRpage = pathname === '/vr';\n    // New coming soon pages\n    const isContactUsPage = pathname === '/contact-us';\n    const isServicesPage = pathname === '/services';\n    const isCre8iveStudioComingSoonPage = pathname === '/cre8ive-studio-comingsoon';\n    const isArchonEngineeringComingSoonPage = pathname === '/archon-engineering-comingsoon';\n    const isBaltarFinanceComingSoonPage = pathname === '/baltar-finance-comingsoon';\n    const isZeitgeistMediaComingSoonPage = pathname === '/zeitgeist-media-comingsoon';\n    const isAboutComingSoonPage = pathname === '/about-comingsoon';\n    const isCareersComingSoonPage = pathname === '/careers-comingsoon';\n    const isPrivacyPolicyPage = pathname === '/privacy-policy';\n    const isTermsOfServicePage = pathname === '/terms-of-service';\n    const isCookiesPage = pathname === '/cookies';\n    // Group all new coming soon pages\n    const isAnyNewComingSoonPage = isCre8iveStudioComingSoonPage || isArchonEngineeringComingSoonPage || isBaltarFinanceComingSoonPage || isZeitgeistMediaComingSoonPage || isAboutComingSoonPage || isCareersComingSoonPage || isPrivacyPolicyPage || isTermsOfServicePage || isCookiesPage || isServicesPage;\n    // Dashboard pages that should not have default headers/footers\n    const isClientDashboard = pathname === '/client-dashboard';\n    // Check if any admin page (including nested routes) - comprehensive check\n    const isAnyAdminPage = pathname.startsWith('/admin');\n    // Frontend Web Design pages\n    const isFrontendAboutPage = pathname === '/frontend-web-design/about';\n    const isFrontendServicesPage = pathname === '/frontend-web-design/services';\n    const isFrontendPortfolioPage = pathname === '/frontend-web-design/portfolio';\n    const isFrontendPricingPage = pathname === '/frontend-web-design/pricing';\n    const isFrontendFAQPage = pathname === '/frontend-web-design/faq';\n    const isFrontendClientPortalPage = pathname === '/frontend-web-design/client-portal';\n    // Check if any frontend web design page\n    const isAnyFrontendPage = isFrontendDesign || isFrontendAboutPage || isFrontendServicesPage || isFrontendPortfolioPage || isFrontendPricingPage || isFrontendFAQPage || isFrontendClientPortalPage || isFrontendContactUsPage || isFrontendComingSoonPage;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full overflow-x-hidden bg-black text-white font-sans min-h-screen\",\n        children: isAnyAdminPage || isClientDashboard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"w-full\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n            lineNumber: 98,\n            columnNumber: 9\n        }, this) : /* ✅ Special handling for Savour & Sip pages */ isAnySavourPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                isSavourAndSip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SavourAndSip_PageAnimationComponent_PageAnimation__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                    lineNumber: 103,\n                    columnNumber: 30\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 w-full\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-1 w-full\",\n                    children: [\n                        !isSavourAndSipComingSoonPage && !isAuthPage && !isAnyFrontendPage && !isTransacComingSoonPage && !isLeModeCoPage && !isLeModeCoComingSoonPage && !isLeModeCoContactUsPage && !isLeModeCoSubscribePage && !isAnyConsumerPulsePage && !isVRpage && !isAnyNewComingSoonPage && (isTransac ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Transac_HeaderComponent_TransacHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 123,\n                            columnNumber: 23\n                        }, this) : isHome || isContactUsPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetaStyleComponents_MetaHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 125,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavBarComponent_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 126,\n                            columnNumber: 25\n                        }, this)),\n                        isAnyFrontendPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FrontendWebDesign_HeaderComponent_HeaderComponent__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 130,\n                            columnNumber: 37\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 w-full\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 133,\n                            columnNumber: 15\n                        }, this),\n                        !isSavourAndSipComingSoonPage && !isTransac && !isAnyFrontendPage && !isTransacComingSoonPage && !isComingSoonPage && !isAuthPage && !isLeModeCoPage && !isLeModeCoComingSoonPage && !isLeModeCoContactUsPage && !isLeModeCoSubscribePage && !isAnyConsumerPulsePage && !isVRpage && !isAnyNewComingSoonPage && !isContactUsPage && (isHome ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetaStyleComponents_MetaFooter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 151,\n                            columnNumber: 23\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FooterComponent_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                            lineNumber: 152,\n                            columnNumber: 23\n                        }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                    lineNumber: 109,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n                lineNumber: 108,\n                columnNumber: 11\n            }, this)\n        }, void 0, false)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\baltar\\\\baltar-inc\\\\apps\\\\app\\\\components\\\\layoutClient.js\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/layoutClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Ccomponents%5C%5ClayoutClient.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Ccomponents%5C%5ClayoutClient.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/layoutClient.js */ \"(ssr)/./app/components/layoutClient.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Ccomponents%5C%5ClayoutClient.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cabc%5C%5CDesktop%5C%5Cbaltar%5C%5Cbaltar-inc%5C%5Capps%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cabc%5CDesktop%5Cbaltar%5Cbaltar-inc%5Capps&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();