/* Dashboard Container */
.dashboardContainer {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebarHeader {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(192, 192, 192, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.logoIcon {
  font-size: 1.5rem;
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logoText {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  letter-spacing: -0.02em;
}

.adminInfo {
  color: #c0c0c0;
  font-size: 0.875rem;
  margin: 0;
}

/* Sidebar Navigation */
.sidebarNav {
  flex: 1;
  padding: 1rem 0;
}

.navItem {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #c0c0c0;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.navItem:hover {
  background: rgba(192, 192, 192, 0.1);
  color: white;
}

.navItem.active {
  background: linear-gradient(90deg, rgba(192, 192, 192, 0.2), rgba(169, 169, 169, 0.1));
  color: white;
  border-right: 3px solid #c0c0c0;
}

.navIcon {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.sidebarFooter {
  padding: 1rem 0;
  border-top: 1px solid rgba(192, 192, 192, 0.1);
}

.logoutButton {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #ef4444;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.logoutButton:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Main Content */
.mainContent {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
}

.contentHeader {
  background: white;
  padding: 2rem 2.5rem;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pageTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: -0.02em;
}

.contentBody {
  flex: 1;
  padding: 2.5rem;
  overflow-y: auto;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f8f9fa;
  gap: 1rem;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #c0c0c0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Overview Tab */
.overviewGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.statIcon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 12px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statContent h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statNumber {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: -0.02em;
}

/* Tab Content */
.tabContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.tabHeader {
  display: flex;
  justify-content: between;
  align-items: center;
  gap: 1rem;
}

.tabHeader h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.generateInvoiceBtn {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generateInvoiceBtn:hover {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Quotes Grid */
.quotesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.emptyState {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Quote Cards */
.quoteCard {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.quoteCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.quoteHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusBadge.pending {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.statusBadge.quoted {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.quoteDate {
  font-size: 0.875rem;
  color: #6b7280;
}

.quoteContent h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
}

.quoteEmail {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.quoteDetails {
  margin: 1rem 0;
}

.quoteDetails p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #4b5563;
}

.quoteMessage {
  background: #f9fafb;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #4b5563;
  margin: 1rem 0;
  border-left: 3px solid #c0c0c0;
}

.quoteActions {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.quoteButton {
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.quoteButton:hover {
  background: linear-gradient(135deg, #a9a9a9, #909090);
  transform: translateY(-1px);
}

.quotedInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quotedInfo p {
  margin: 0;
  font-weight: 600;
  color: #1a1a1a;
}

.resendButton {
  background: none;
  border: 1px solid #c0c0c0;
  color: #c0c0c0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resendButton:hover {
  background: #c0c0c0;
  color: white;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  overflow-y: auto;
  padding: 2rem;
}

.modal {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  margin: auto;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.modalHeader h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #f3f4f6;
  color: #1a1a1a;
}

.quoteForm {
  padding: 1rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.input, .textarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  color: #1a1a1a;
  transition: all 0.2s ease;
  outline: none;
}

.input:focus, .textarea:focus {
  border-color: #c0c0c0;
  box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.cancelButton {
  background: none;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.submitButton {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
}

.submitButton:disabled, .cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.errorMessage {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  margin: 0 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }
  
  .mainContent {
    margin-left: 240px;
  }
  
  .quotesGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .mainContent {
    margin-left: 0;
  }

  .contentHeader {
    padding: 1.5rem;
  }

  .contentBody {
    padding: 1.5rem;
  }

  .overviewGrid {
    grid-template-columns: 1fr;
  }

  .modalOverlay {
    padding: 1rem;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .modal {
    width: 95%;
    max-height: 85vh;
    margin-top: 0;
  }

  .quoteForm {
    max-height: calc(85vh - 100px);
  }
}
