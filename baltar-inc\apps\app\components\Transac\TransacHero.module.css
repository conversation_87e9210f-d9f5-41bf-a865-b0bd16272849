/* TransacHero.module.css - Inspired by Interac aesthetic */

.hero {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: #f8f8f6; /* light beige background */
  box-sizing: border-box;
}

.heroVideo {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  z-index: 0;
  opacity: 0.6;
  pointer-events: none;
}

.heroContent {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.75);
  box-sizing: border-box;
  overflow-x: hidden;
  backdrop-filter: blur(8px);
}

.heroContent h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  max-width: 900px;
  color: #0f1115;
}

.heroContent p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  max-width: 700px;
  color: #2d2d2d;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.buttonPrimary {
  padding: 0.75rem 1.5rem;
  background-color: #ffc600; /* Interac gold */
  color: black;
  border-radius: 0.375rem;
  font-weight: 600;
  text-decoration: none;
  transition: background 0.3s;
  white-space: nowrap;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.buttonPrimary:hover {
  background-color: #ffda3a;
}

.buttonSecondary {
  padding: 0.75rem 1.5rem;
  border: 2px solid #ffc600;
  color: #0f1115;
  border-radius: 0.375rem;
  font-weight: 500;
  text-decoration: none;
  transition: background 0.3s, color 0.3s;
  white-space: nowrap;
}

.buttonSecondary:hover {
  background-color: #ffc600;
  color: #0f1115;
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroContent h1 {
    font-size: 1.8rem;
  }

  .heroContent p {
    font-size: 1rem;
  }

  .buttonPrimary,
  .buttonSecondary {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
  }
}