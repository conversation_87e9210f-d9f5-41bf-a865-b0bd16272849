/* Header Main */
.header {
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  font-family: 'Helvetica Neue', sans-serif;
  transition: all 0.5s ease;

}

/* Navbar */
.navBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 50px;
}

/* Sections */
.navLeft, .navRight {
  flex: 1;
  display: flex;
  align-items: center;
}

.navLeft {
  justify-content: flex-start;
}

.navRight {
  justify-content: flex-end;
}

/* Logo */
.logo {
  text-align: center;
  position: relative;
}

.logo a {
  text-decoration: none;
  color: #000;
}

.logo h1 {
  font-family: 'EB Garamond', 'Georgia', 'Times New Roman', serif; /* Elegant fonts */
  font-size: 30px;
  letter-spacing: 5px;
  margin: 0;
  font-weight: 700;
  white-space: nowrap;
}

/* Contact Link */
.contactLink {
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  color: #000;
  transition: 0.3s;
}

.contactLink:hover {
  opacity: 0.7;
}

/* Icons */
.navIcons {
  display: flex;
  align-items: center;
  gap: 20px;
}

.icon {
  width: 22px;
  cursor: pointer;
  transition: 0.3s;
}

.icon:hover {
  transform: scale(1.1);
}


.menuToggle {
  font-family: 'EB Garamond', 'Georgia', 'Times New Roman', serif;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 4px;
  text-transform: uppercase;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #000;
}


.hamburgerSymbol {
  margin-left: 5px;
  font-size: 24px;
}

/* Menu Wrapper */
.menuWrapper {
  position: fixed;
  top: 0;
  right: 0;
  width: 60vw;
  height: 100vh;
  background: white;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 6rem 2rem 4rem 2rem;
  overflow-y: auto;
  box-shadow: -8px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* Dropdown Menu */
.menuDropdown {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 0;
  margin: 0;
  width: 100%;
}

.menuDropdown li {
  text-align: left;
}

.menuDropdown li a {
  font-size: 1.3rem;
  text-decoration: none;
  color: #000;
  font-weight: 500;
  display: block;
  transition: color 0.3s;
}

.menuDropdown li a:hover {
  color: gray;
}

/* Scrolled Header */
.scrolled {
  background: #f5f5f5;
  padding: 20px 50px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.5s ease;
}

@media (max-width: 768px) {
  .navBar {
    padding: 1rem 1.5rem;
    flex-wrap: nowrap;
    position: relative;
  }

  .menuWrapper {
    width: 105vw; 
    padding: 4rem 1.5rem 2rem 1.5rem; 
  }

  .header {
    padding-top: 1.5rem; 
    padding-bottom: 1.5rem; 
  }
  .navLeft {
    display: none; 
  }

  .logo {
    position: absolute;
    left: 30%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .logo h1 {
    font-family: 'EB Garamond', 'Georgia', 'Times New Roman', serif; 
    font-size: 26px;
    font-weight: 400; 
    letter-spacing: 4px; 
    text-transform: uppercase; 
    margin: 0;
    color: #000; 
  }
  

  .navRight {
    flex: 0 0 auto;
    justify-content: flex-end;
    gap: 10px;
  }

  .navIcons {
    gap: 0.8rem;
    position: absolute;
    left: 65%;

  }

  .icon {
    display: none; 
  }

  .menuToggle {
    font-size: 12px;
  }

  .hamburgerSymbol {
    font-size: 20px;
  }
}

@keyframes vibrate {
  0% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
  100% { transform: translate(0); }
}
