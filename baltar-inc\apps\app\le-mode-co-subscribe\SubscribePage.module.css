/* Subscribe Page Styles */
.subscribeContainer {
  min-height: 100vh;
  background: #fefefe;
  color: #111;
  font-family: 'Helvetica Neue', sans-serif;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hero Section */
.hero {
  text-align: center;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.hero h1 {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 1rem;
  color: #000;
  letter-spacing: 2px;
}

.hero p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Package Selection */
.packageSelection {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.packageSelection h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 3rem;
  color: #000;
  letter-spacing: 1px;
}

.packagesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.packageCard {
  background: #ffffff;
  border-radius: 1.5rem;
  padding: 2rem;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: visible;
}

.packageCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-color: #d4af37;
}

.packageCard.popular {
  border-color: #d4af37;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.15);
}

.badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #d4af37;
  color: #000;
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.packageCard h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
  text-align: center;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: #d4af37;
  text-align: center;
  margin-bottom: 1rem;
}

.description {
  color: #666;
  margin-bottom: 1.5rem;
  text-align: center;
  line-height: 1.6;
}

.features {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.features li {
  padding: 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.bestFor {
  color: #888;
  font-style: italic;
  text-align: center;
  margin: 1.5rem 0;
  font-size: 0.9rem;
}

.selectBtn {
  width: 100%;
  padding: 1rem 2rem;
  background: transparent;
  border: 2px solid #000;
  color: #000;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
}

.selectBtn:hover {
  background: #000;
  color: #fff;
}

/* Form Container */
.formContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.backBtn {
  background: none;
  border: 1px solid #ddd;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  color: #666;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.backBtn:hover {
  background: #f5f5f5;
  border-color: #d4af37;
}

.selectedPackage {
  text-align: right;
}

.selectedPackage h3 {
  margin: 0 0 0.5rem 0;
  color: #000;
  font-size: 1.25rem;
}

.selectedPackage span {
  color: #d4af37;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Subscription Form */
.subscriptionForm {
  background: #ffffff;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.formSection {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.formSection:last-of-type {
  border-bottom: none;
}

.formSection h4 {
  margin: 0 0 1.5rem 0;
  color: #000;
  font-size: 1.2rem;
  font-weight: 600;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.cardElement {
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
  transition: border-color 0.2s ease;
}

.cardElement:focus-within {
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.errorMessage {
  background: #fee;
  color: #c53030;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #fed7d7;
  font-size: 0.9rem;
}

.formActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.total {
  font-size: 1.2rem;
  font-weight: 600;
  color: #000;
}

.submitBtn {
  padding: 1rem 2rem;
  background: #d4af37;
  color: #000;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.submitBtn:hover:not(:disabled) {
  background: #b8941f;
  transform: translateY(-2px);
}

.submitBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Success Container */
.successContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
}

.successCard {
  background: #ffffff;
  border-radius: 1.5rem;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.successIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.successCard h2 {
  color: #000;
  margin-bottom: 1rem;
  font-size: 2rem;
  font-weight: 600;
}

.successCard p {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.successActions {
  margin-top: 2rem;
}

.homeBtn {
  padding: 1rem 2rem;
  background: #d4af37;
  color: #000;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.homeBtn:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
  
  .packageSelection {
    padding: 2rem 1rem;
  }
  
  .packagesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .formContainer {
    padding: 1rem;
  }
  
  .formHeader {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .selectedPackage {
    text-align: center;
  }
  
  .formActions {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .submitBtn {
    width: 100%;
  }
  
  .successCard {
    padding: 2rem;
    margin: 1rem;
  }
}
