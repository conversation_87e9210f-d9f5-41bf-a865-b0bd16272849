.headlineSection {
    display: flex;
    flex-direction: column;
    padding: 2rem 1.5rem;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
  }
  
  .imageWrapper {
    width: 40%;
    overflow: hidden;
  }
  
  .image {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 4px;
  }
  
  .content {
    margin-top: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.3;
    color: #000;
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
    color: #444;
    font-weight: 400;
  }

  .loadingState {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }

  .loadingPlaceholder {
    font-size: 1.1rem;
    color: #666;
    animation: pulse 1.5s ease-in-out infinite;
  }

  .metadata {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    align-items: center;
  }

  .category {
    background: #f0f0f0;
    color: #333;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .sentiment {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
  }

  .sentiment[data-sentiment="POSITIVE"] {
    background: #e8f5e8;
    color: #2e7d32;
  }

  .sentiment[data-sentiment="NEGATIVE"] {
    background: #ffebee;
    color: #c62828;
  }

  .sentiment[data-sentiment="NEUTRAL"] {
    background: #f5f5f5;
    color: #666;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
  