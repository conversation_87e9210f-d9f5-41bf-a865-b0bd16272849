/* Le-Mode-Co Tab Styles */
.leModeCoContainer {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tab Header */
.tabHeader {
  background: white;
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tabHeader h2 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.75rem;
  font-weight: 600;
}

.subTabs {
  display: flex;
  gap: 0.5rem;
}

.subTab {
  padding: 0.75rem 1.5rem;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.subTab:hover {
  background: #f7fafc;
  border-color: #d4af37;
}

.subTab.active {
  background: #d4af37;
  color: white;
  border-color: #d4af37;
}

/* Tab Content */
.tabContent {
  padding: 2rem;
}

/* Overview Section */
.overviewSection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.statCard {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.statIcon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 0.5rem;
  color: white;
}

.statIcon svg {
  width: 24px;
  height: 24px;
}

.statContent h3 {
  margin: 0 0 0.5rem 0;
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statNumber {
  margin: 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
}

.quickActions {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quickActions h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.actionBtn {
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.actionBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* Packages Section */
.packagesSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f1f5f9;
  margin-bottom: 1.5rem;
}

.sectionHeader h3 {
  margin: 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.createBtn {
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.createBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.packagesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.packageCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.packageCard:hover {
  transform: translateY(-2px);
}

.packageCard.inactive {
  opacity: 0.6;
  border: 2px dashed #e2e8f0;
}

.packageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.packageHeader h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.popularBadge {
  background: #d4af37;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.packagePrice {
  font-size: 1.5rem;
  font-weight: 700;
  color: #d4af37;
  margin-bottom: 1rem;
}

.packageFeatures {
  margin-bottom: 1.5rem;
}

.feature {
  color: #4a5568;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.moreFeatures {
  color: #718096;
  font-size: 0.75rem;
  font-style: italic;
}

.packageActions {
  display: flex;
  gap: 0.75rem;
}

.editBtn {
  flex: 1;
  padding: 0.625rem 1.25rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editBtn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.toggleBtn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.toggleBtn.active {
  background: #f56565;
  color: white;
}

.toggleBtn.active:hover {
  background: #e53e3e;
}

.toggleBtn.inactive {
  background: #48bb78;
  color: white;
}

.toggleBtn.inactive:hover {
  background: #38a169;
}

/* Subscriptions Section */
.subscriptionsSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.subscriptionsTable {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tableHeader {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: #f7fafc;
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tableRow {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  align-items: center;
}

.tableRow:last-child {
  border-bottom: none;
}

.customerInfo {
  display: flex;
  flex-direction: column;
}

.customerName {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.customerEmail {
  font-size: 0.875rem;
  color: #718096;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
}

.rowActions {
  display: flex;
  gap: 0.5rem;
}

.viewBtn {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.viewBtn:hover {
  background: #3182ce;
}

.statusCell {
  display: flex;
  align-items: center;
}

.statusSelect {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  cursor: pointer;
  transition: opacity 0.2s;
}

.statusSelect:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.statusSelect option {
  background: white;
  color: black;
}

/* Orders Section */
.ordersSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.customerDetails {
  display: flex;
  gap: 1rem;
  color: #718096;
  font-size: 0.875rem;
}

.customerDetails span {
  background: #f7fafc;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
}

.ordersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.orderCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.orderHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.orderHeader h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.orderItems {
  margin-bottom: 1.5rem;
}

.orderItems h5 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1rem;
}

.orderItem {
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  margin-bottom: 0.75rem;
}

.itemInfo strong {
  color: #2d3748;
  display: block;
  margin-bottom: 0.25rem;
}

.itemInfo p {
  margin: 0 0 0.5rem 0;
  color: #718096;
  font-size: 0.875rem;
}

.categoryTag {
  background: #d4af37;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.addItemForm {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.addItemForm input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.formActions {
  display: flex;
  gap: 0.5rem;
}

.saveBtn {
  padding: 0.5rem 1rem;
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.saveBtn:hover {
  background: #38a169;
}

.cancelBtn {
  padding: 0.5rem 1rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.cancelBtn:hover {
  background: #cbd5e0;
}

.orderActions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.addItemBtn {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.addItemBtn:hover {
  background: #3182ce;
}

.completeBtn {
  padding: 0.5rem 1rem;
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.completeBtn:hover {
  background: #38a169;
}

.notifyBtn {
  padding: 0.5rem 1rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.notifyBtn:hover {
  background: #b8941f;
}

.notifiedBadge {
  padding: 0.5rem 1rem;
  background: #c6f6d5;
  color: #22543d;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Shipping Warning Styles */
.shippingWarning {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.warningIcon {
  color: #92400e;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.warningText {
  flex: 1;
}

.warningText strong {
  color: #92400e;
  font-weight: 600;
  display: block;
  margin-bottom: 0.25rem;
}

.warningText p {
  margin: 0;
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.4;
}

.disabledBtn {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: #9ca3af !important;
}

.disabledBtn:hover {
  transform: none !important;
  box-shadow: none !important;
}

.emptyState {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

.loading {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modalHeader h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.closeBtn:hover {
  background: #f7fafc;
}

.modalForm {
  padding: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.featureInput {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.featureInput input {
  flex: 1;
  margin-bottom: 0;
}

.removeFeatureBtn {
  background: #f56565;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}

.removeFeatureBtn:hover {
  background: #e53e3e;
}

.addFeatureBtn {
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.addFeatureBtn:hover {
  background: #3182ce;
}

.checkboxLabel {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.modalActions .cancelBtn {
  background: #e2e8f0;
  color: #4a5568;
}

.modalActions .saveBtn {
  background: #d4af37;
  color: white;
}

.modalActions .saveBtn:hover {
  background: #b8941f;
}

/* Category Management Styles */
.categoryManagement {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.categoriesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.categoryCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.categoryCard:hover {
  transform: translateY(-2px);
}

.categoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.categoryHeader h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
}

.categoryActions {
  display: flex;
  gap: 0.5rem;
}

.categoryDescription {
  color: #718096;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.categoryMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status.active {
  background: #c6f6d5;
  color: #22543d;
}

.status.inactive {
  background: #fed7d7;
  color: #742a2a;
}

.order {
  color: #718096;
  font-weight: 500;
}

.deleteBtn {
  padding: 0.5rem 1rem;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.deleteBtn:hover {
  background: #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tabContent {
    padding: 1rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .packagesGrid {
    grid-template-columns: 1fr;
  }

  .ordersGrid {
    grid-template-columns: 1fr;
  }

  .categoriesGrid {
    grid-template-columns: 1fr;
  }

  .tableHeader,
  .tableRow {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .tableHeader {
    display: none;
  }

  .tableRow {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 1rem;
  }

  .subTabs {
    flex-wrap: wrap;
  }

  .modal {
    width: 95%;
    margin: 1rem;
  }

  .categoryActions {
    flex-direction: column;
  }

  .categoryMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
