.comingSoonContainer {
    height: 100vh;
    background-color: #0d0f14;
    color: white;
    font-family: 'Helvetica Neue', sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    padding-top: 180px;
  }
  
  /* Top Header Bar */
  .topBar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    font-size: 1rem;
    font-weight: 500;
    border-bottom: 1px solid #222;
  }
  
  .brand {
    color:rgb(255, 255, 255);
    font-size: 1.1rem;
    font-weight: bold;
  }
  
  .tagline {
    color: #bbb;
    font-size: 0.95rem;
  }
  
  .title {
    font-size: 3rem;
    font-weight: bold;
    color:rgb(255, 255, 255);
  }
  
  .subtitle {
    font-size: 1.2rem;
    color: #aaa;
    margin-top: 1rem;
  }
  