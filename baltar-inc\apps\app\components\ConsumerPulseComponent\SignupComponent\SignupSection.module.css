.authContainer {
  width: 100vw;
  height: 100vh;
  background-color: #0d0f14; /* Match Consumer Pulse dark theme */
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
}


.input {
  width: 100%;
  padding: 0.8rem;
  margin: 0.5rem 0;
  background: #111;
  border: 1px solid #999;
  border-radius: 999px;
  color: white;
  outline: none;
}

.row {
  display: flex;
  gap: 1rem;
}

.primaryBtn {
  background:rgb(0, 0, 0);
  border: none;
  color: white;
  padding: 0.8rem;
  width: 100%;
  border-radius: 999px;
  margin: 1rem 0;
  font-weight: bold;
}

.options {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.alt {
  text-align: center;
  margin: 1rem 0 0.5rem;
}

.socialIcons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.socialIcons button {
  background: #222;
  color: white;
  border: 1px solid #fff;
  border-radius: 50%;
  padding: 0.5rem 0.8rem;
  cursor: pointer;
}
.CenterItems {
  text-align: center;
  font-size: 32px;
}
.switchText {
  font-size: 0.85rem;
  text-align: center;
  margin-top: 1rem;
}

.agreement {
  margin: 1rem 0;
  font-size: 0.9rem;
}

.formBox {
  width: 100%;
  max-width: 420px;
  padding: 2rem;
  background-color: #0d0f14;
  display: flex;
  flex-direction: column;
}

