/* File: BubbleHeadComponent.module.css */

.bubbleWrapper {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    width: 100px;
    height: 100px;
    cursor: pointer;
  }
  .rotatingRing {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.65rem;
    text-transform: lowercase;
    white-space: nowrap;
    animation: rotate 5s linear infinite;
    color: #888;
    font-weight: 500;
    pointer-events: none;
  }
  
  @keyframes rotate {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
  
  .faceWrapper {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    overflow: hidden;
    background-color: white;
    position: relative;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
  }
  
  .faceImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
  }