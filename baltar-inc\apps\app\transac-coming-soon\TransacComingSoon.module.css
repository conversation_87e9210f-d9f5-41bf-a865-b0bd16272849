.container {
    background-color: #ffffff;
    color: #1f2937;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem 2rem;
    text-align: center;
    box-sizing: border-box;
  }
  
  .inner {
    max-width: 540px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }
  
  .logo {
    opacity: 1;
    margin-bottom: 1rem;
  }
  
  .heading {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1f2937;
  }
  
  .subtext {
    font-size: 1rem;
    color: #4b5563;
    max-width: 460px;
    line-height: 1.6;
  }
  
  .backButton {
    margin-top: 2rem;
    padding: 0.75rem 1.5rem;
    background-color: #ffcc00;
    color: #000000;
    border-radius: 0.375rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .backButton:hover {
    background-color: #f4c500;
  }
  
  /* 📱 Mobile */
  @media (max-width: 768px) {
    .heading {
      font-size: 1.7rem;
    }
  
    .subtext {
      font-size: 0.95rem;
    }
  
    .backButton {
      width: 100%;
    }
  }
  