.snapshotSection {
  padding: 6rem 2rem;
  background: white;
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.tilesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.tile {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.tile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tile:hover::before {
  transform: scaleX(1);
}

.tile:hover {
  border-color: #667eea;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
  transform: translateY(-5px);
}

.tileIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.tileTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.tileDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .snapshotSection {
    padding: 4rem 1rem;
  }
  
  .tilesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .tile {
    padding: 1.5rem;
  }
  
  .tileIcon {
    font-size: 2.5rem;
  }
  
  .tileTitle {
    font-size: 1.1rem;
  }
  
  .tileDescription {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .tile {
    padding: 1.25rem;
  }
  
  .tileIcon {
    font-size: 2rem;
  }
}
