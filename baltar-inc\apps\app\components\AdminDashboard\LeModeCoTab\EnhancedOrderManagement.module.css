/* Enhanced Order Management Styles - Professional Design */
.orderManagement {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 32px;
  background: #f8fafc;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.headerLeft {
  flex: 1;
}

.headerActions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.header h3 {
  margin: 0 0 12px 0;
  color: #111827;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.customerDetails {
  display: flex;
  gap: 16px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.customerDetails span {
  background: #f3f4f6;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.statusBadge {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.statusBadge.pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.statusBadge.paid {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.statusBadge.failed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.statusBadge.complimentary {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.statusBadge.cancelled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

.createOrderBtn {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

.createOrderBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.35);
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.createOrderBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Empty Orders State */
.emptyOrders {
  background: white;
  border-radius: 16px;
  padding: 64px 48px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.emptyOrdersContent h4 {
  margin: 0 0 16px 0;
  color: #111827;
  font-size: 24px;
  font-weight: 700;
}

.emptyOrdersContent p {
  margin: 0 0 32px 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
}

.createFirstOrderBtn {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

.createFirstOrderBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.35);
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.createFirstOrderBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Enhanced Order Card Styles */
.orderCard {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.orderCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.orderHeader {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid #f3f4f6;
}

.orderHeaderLeft h4 {
  margin: 0 0 8px 0;
  color: #111827;
  font-size: 20px;
  font-weight: 700;
}

.orderDate {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

.orderHeaderRight {
  display: flex;
  align-items: center;
  gap: 16px;
}

.statusSelect {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  background: white;
  color: #374151;
  cursor: pointer;
  min-width: 140px;
  transition: all 0.2s ease;
}

.statusSelect:hover {
  border-color: #9ca3af;
}

.statusSelect:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.expandBtn {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.expandBtn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* Order Summary */
.orderSummary {
  display: flex;
  gap: 1.5rem;
  padding: 1rem 0;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summaryLabel {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  font-weight: 600;
}

.summaryValue {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.shippingWarning {
  background: #fef3c7;
  color: #92400e;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #fbbf24;
}

/* Order Details */
.orderDetails {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
  margin-top: 1rem;
  max-width: 100%;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.detailsSection h5 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1rem;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailLabel {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  font-weight: 600;
}

.detailValue {
  font-size: 0.875rem;
  color: #2d3748;
  font-weight: 500;
}

/* Items Section */
.itemsSection {
  margin-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.itemsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.itemsHeader h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1rem;
}

.itemActions {
  display: flex;
  gap: 0.75rem;
}

.addProductBtn, .addTemplateBtn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addProductBtn:hover, .addTemplateBtn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.addTemplateBtn {
  background: #38b2ac;
}

.addTemplateBtn:hover {
  background: #319795;
}

/* Items List */
.itemsList {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  max-width: 100%;
  box-sizing: border-box;
}

.emptyItems {
  text-align: center;
  color: #718096;
  padding: 2rem;
}

.emptyItems p {
  margin: 0.5rem 0;
}

.orderItem {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
  min-width: 0;
}

.orderItem:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.orderItem:last-child {
  margin-bottom: 0;
}

.itemInfo {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.itemName {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.4;
}

.itemDescription {
  color: #718096;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.itemMeta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.itemCategory, .itemQuantity, .itemValue {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.itemValue {
  background: #c6f6d5;
  color: #22543d;
}

.removeItemBtn {
  background: #fed7d7;
  color: #c53030;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.removeItemBtn:hover {
  background: #feb2b2;
  transform: scale(1.1);
}

.loading {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

.emptyState {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

.emptyStateContent h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.emptyStateContent p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

.emptyStateHint {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #d4af37;
}

.emptyStateHint p {
  margin: 0;
  font-size: 0.875rem;
  color: #4a5568;
}

.ordersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 1.5rem;
}

/* Enhanced Order Card */
.orderCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.orderHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.orderHeader h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.statusContainer {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.statusBtn {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.statusBtn:hover {
  background: #3182ce;
}

.orderMeta {
  padding: 0 1.5rem 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.orderMetaRow {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
  flex: 1;
}

.metaLabel {
  font-weight: 500;
  color: #718096;
  min-width: 80px;
}

.metaValue {
  font-weight: 600;
  color: #2d3748;
  word-wrap: break-word;
  overflow-wrap: break-word;
  min-width: 0;
}

.metaItem:first-child .metaValue {
  color: #d4af37;
}

.metaItem:nth-child(2) .metaValue {
  color: #4299e1;
}

.orderItems {
  padding: 1.5rem;
}

.itemsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.itemsHeader h5 {
  margin: 0;
  color: #4a5568;
  font-size: 1rem;
}

.itemActions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.actionGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.actionLabel {
  font-size: 0.75rem;
  color: #718096;
  font-weight: 500;
}

.templateBtn {
  padding: 0.5rem 1rem;
  background: #9f7aea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.templateBtn:hover {
  background: #805ad5;
}

.addItemBtn {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.addItemBtn:hover {
  background: #3182ce;
}

.orderItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  position: relative;
}

.itemImage {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.itemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.noImage {
  color: #a0aec0;
  font-size: 0.75rem;
  text-align: center;
}

.itemInfo {
  flex: 1;
}

.itemInfo strong {
  color: #2d3748;
  display: block;
  margin-bottom: 0.25rem;
}

.itemInfo p {
  margin: 0 0 0.5rem 0;
  color: #718096;
  font-size: 0.875rem;
}

.itemMeta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.categoryTag {
  background: #d4af37;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.quantity {
  background: #4299e1;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.value {
  background: #48bb78;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.removeBtn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.removeBtn:hover {
  background: #e53e3e;
}

.orderActions {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  justify-content: flex-end;
}

.notifyBtn {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background 0.2s;
}

.notifyBtn:hover {
  background: #b8941f;
}

.notifiedBadge {
  padding: 0.75rem 1.5rem;
  background: #c6f6d5;
  color: #22543d;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  width: 900px;
  max-width: 95vw;
  height: 80vh;
  max-height: 700px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modalHeader h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.closeBtn:hover {
  background: #f7fafc;
}

.modalContent {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.statusSelect {
  background: white;
}

.statusInfo {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.statusInfo h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.statusInfo ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #4a5568;
  font-size: 0.875rem;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.cancelBtn {
  padding: 0.75rem 1.5rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.cancelBtn:hover {
  background: #cbd5e0;
}

.updateBtn,
.addBtn,
.applyBtn {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.updateBtn:hover:not(:disabled),
.addBtn:hover:not(:disabled),
.applyBtn:hover:not(:disabled) {
  background: #b8941f;
}

.updateBtn:disabled,
.addBtn:disabled,
.applyBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Product Search Modal */
.searchSection {
  margin-bottom: 1.5rem;
}

.searchFilters {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.searchInput {
  flex: 2;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
}

.categoryFilter {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
}

.searchHint {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0;
  font-style: italic;
}

.productsList {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.noProducts {
  padding: 2rem;
  text-align: center;
  color: #64748b;
  font-style: italic;
}

.productItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: background 0.2s;
}

.productItem:hover {
  background: #f7fafc;
}

.productItem.selected {
  background: #e6fffa;
  border-color: #38b2ac;
}

.productItem:last-child {
  border-bottom: none;
}

.productImage {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.productInfo h4 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 1rem;
}

.productInfo p {
  margin: 0;
  color: #718096;
  font-size: 0.875rem;
}

.price {
  color: #d4af37;
  font-weight: 600;
}

.stock {
  color: #4a5568;
}

.selectionSection {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
}

.selectionSection h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
}

.quantitySection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantitySection label {
  margin: 0;
  color: #4a5568;
  font-size: 0.875rem;
}

.quantitySection input {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

/* Template Selection Modal */
.templatesList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.templateCard {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
  background: white;
}

.templateCard:hover {
  border-color: #d4af37;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.1);
}

.templateInfo h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
}

.templateInfo p {
  margin: 0 0 1rem 0;
  color: #718096;
  font-size: 0.875rem;
}

.templateMeta {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.templateType,
.season,
.itemCount,
.totalValue {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.templateType {
  background: #e6fffa;
  color: #234e52;
}

.season {
  background: #fef5e7;
  color: #744210;
}

.itemCount {
  background: #e6f3ff;
  color: #1a365d;
}

.totalValue {
  background: #f0fff4;
  color: #22543d;
}

.templatePreview {
  margin: 1rem 0;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
}

.templatePreview h5 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.templateItems {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.templateItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.itemImage {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.itemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.itemDetails {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.itemName {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.itemBrand {
  color: #718096;
  font-size: 0.75rem;
}

.itemPrice {
  color: #d4af37;
  font-weight: 600;
  font-size: 0.875rem;
}

.itemQuantity {
  color: #4a5568;
  font-size: 0.75rem;
  background: #edf2f7;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  align-self: flex-start;
}

.templateActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.templateSummary {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.templateSummary span:first-child {
  color: #4a5568;
}

.templateSummary span:last-child {
  color: #d4af37;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ordersGrid {
    grid-template-columns: 1fr;
  }
  
  .orderHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .statusContainer {
    width: 100%;
    justify-content: space-between;
  }
  
  .itemActions {
    flex-direction: column;
    width: 100%;
  }
  
  .orderActions {
    flex-direction: column;
  }
  
  .modal {
    width: 95vw !important;
    height: 90vh !important;
    max-height: 90vh !important;
    margin: 1rem;
  }

  .modalActions {
    flex-direction: column;
  }

  .itemsList {
    display: flex !important;
    flex-direction: column !important;
  }
}

/* Stable Desktop Layout */
@media (min-width: 769px) {
  .modal {
    width: 900px !important;
    max-width: 95vw !important;
    height: 80vh !important;
    max-height: 700px !important;
  }
}
