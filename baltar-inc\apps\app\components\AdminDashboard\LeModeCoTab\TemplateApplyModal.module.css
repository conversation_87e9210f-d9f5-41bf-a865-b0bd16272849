.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  max-width: 800px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modalHeader h3 {
  margin: 0;
  color: #111827;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.closeButton {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  color: #6b7280;
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #e2e8f0;
  color: #334155;
}

.modalBody {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem 2rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-size: 1.125rem;
}

.emptyState {
  text-align: center;
  padding: 3rem;
}

.emptyState h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.25rem;
}

.emptyState p {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}

.templatesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.templateCard {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background: #fafafa;
}

.templateCard:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.templateCard.selected {
  border-color: #10b981;
  background: #f0fdf4;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.templateHeader {
  margin-bottom: 1rem;
}

.templateHeader h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 700;
}

.templateMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.itemCount {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.templateValue {
  color: #059669;
  font-weight: 700;
  font-size: 1rem;
}

.templateDescription {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  font-style: italic;
}

.templateItems h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.templateItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.itemName {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.itemQuantity {
  color: #6b7280;
  font-size: 0.75rem;
  background: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
}

.moreItems {
  color: #6b7280;
  font-size: 0.75rem;
  text-align: center;
  padding: 0.5rem;
  font-style: italic;
}

.selectedBadge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #10b981;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.selectedTemplate {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.selectedTemplate h4 {
  margin: 0 0 0.5rem 0;
  color: #0369a1;
  font-size: 1rem;
}

.selectedTemplate p {
  margin: 0 0 0.75rem 0;
  color: #0369a1;
}

.warningNote {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #92400e;
}

.warningNote strong {
  font-weight: 600;
}

.modalFooter {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.cancelButton {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.applyButton {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.applyButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.applyButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}
