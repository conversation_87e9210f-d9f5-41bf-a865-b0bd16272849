/* Header Navbar */
.headerNavbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.menuButton {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.menuButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.locationButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.locationButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Sidebar Menu */
.menuBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  backdrop-filter: blur(5px);
}

.sidebarMenu {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  z-index: 1002;
  padding: 2rem;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

.menuSections {
  margin-top: 3rem;
}

.leftMenu h3 {
  color: #d4af37;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.leftMenu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.leftMenu li {
  color: white;
  padding: 1rem 0;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.leftMenu li:hover {
  color: #d4af37;
  padding-left: 1rem;
  transform: translateX(5px);
}

.leftMenu li.active {
  color: #d4af37;
  font-weight: 600;
}

.leftMenu li.active::before {
  content: '';
  position: absolute;
  left: -1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  border-radius: 2px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .headerNavbar {
    padding: 1rem;
  }

  .sidebarMenu {
    width: 100vw;
    height: 100vh;
    padding: 1rem;
    box-sizing: border-box;
    overflow-y: auto;
    left: 0;
    right: 0;
  }

  .menuSections {
    margin-top: 2rem;
    width: 100%;
    box-sizing: border-box;
  }

  .leftMenu {
    width: 100%;
    box-sizing: border-box;
  }

  .leftMenu h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    word-wrap: break-word;
  }

  .leftMenu li {
    font-size: 1rem;
    padding: 0.75rem 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
  }

  .leftMenu li:hover {
    padding-left: 0.5rem;
    transform: translateX(3px);
  }

  .menuButton {
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
  }

  .locationButton {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .headerNavbar {
    padding: 0.75rem;
  }

  .sidebarMenu {
    padding: 0.75rem;
  }

  .menuSections {
    margin-top: 1.5rem;
  }

  .leftMenu h3 {
    font-size: 1.2rem;
  }

  .leftMenu li {
    font-size: 0.95rem;
    padding: 0.6rem 0;
  }

  .locationButton {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
    max-width: 100px;
  }

  .closeButton {
    top: 0.5rem;
    right: 0.5rem;
    font-size: 1.8rem;
  }
}
