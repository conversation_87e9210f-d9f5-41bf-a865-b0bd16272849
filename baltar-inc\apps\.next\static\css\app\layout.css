/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/components/NavBarComponent/navbar.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* Base Navbar */
.navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #f3f4f6;
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.navbar-content {
  max-width: 1150px;
  margin: 0 auto;
  display: flex;

  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

/* Logo */
.logo-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #1f2937;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
}

/* Hamburger */
/* Hamburger Button Style */
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  gap: 5px;
  background: none;
  border: none;
  border-radius: 6px;
  padding: 0px;
  position: absolute; /* 👉 Important */
  top: 1rem;           /* 👉 thoda upar */
  right: 6rem;       /* 👉 screen ke thoda andar */
  z-index: 2000;       /* 👉 hamesha upar dikhna chahiye */
}

/* Hamburger lines */
.bar {
  width: 25px;
  height: 3px;
  background-color: #333;
  border-radius: 2px;
}

/* Navbar links */
.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-item-wrapper {
  position: relative;
}

.nav-item {
  font-weight: 500;
  color: #4b5563;
  text-decoration: none;
  cursor: pointer;
}

.nav-item:hover {
  color: #111827;
}

/* Dropdown */
.dropdown {
  position: absolute;
  top: 2.5rem;
  left: 0;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 200px;
  z-index: 999;
  box-shadow: 0px 4px 12px rgba(0,0,0,0.1);
}

.dropdown-item {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #4b5563;
  text-decoration: none;
}

.dropdown-item:hover {
  background-color: #bebeba;
  color: #1f2937;
}

/* 📱 Mobile */
.mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 80%;
  height: 100%;
  background-color: white;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  box-shadow: -5px 0 15px rgba(0,0,0,0.1);
  z-index: 2000;
  animation: slideIn 0.3s ease-in-out;
}

.menu-slide {
  animation: slideOut 0.3s ease-in-out reverse;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0%); }
}

@keyframes slideOut {
  from { transform: translateX(0%); }
  to { transform: translateX(100%); }
}

/* Mobile Dropdown */
.mobile-dropdown-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-dropdown-toggle {
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  text-align: left;
  cursor: pointer;
}

.mobile-dropdown {
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Close Button */
.close-btn {
  align-self: flex-end;
  font-size: 2rem;
  background: none;
  border: none;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }
  
  .nav-links {
    display: none;
  }

  .nav-links.open {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding-top: 1rem;
  }

  .navbar-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/components/FooterComponent/Footer.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
.footer {
  background-color: #fff;
  border-top: 1px solid #e5e7eb;
  width: 100%;
  padding: 4rem 2rem;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.03);
}

.footer-content {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-left {
  flex: 1 1 55%;
}

.footer-heading {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.footer-heading .highlight {
  color: #6366f1;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.name-fields {
  display: flex;
  gap: 1rem;
}

.name-fields input {
  flex: 1;
}

.contact-form input,
.contact-form textarea {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: #fff;
  color: #1f2937;
}

.contact-form textarea {
  resize: none;
  height: 100px;
}

.contact-form button {
  width: -moz-fit-content;
  width: fit-content;
  padding: 0.75rem 1.5rem;
  background-color: #6366f1;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.contact-form button:hover {
  background-color: #4f46e5;
}

/* Right side */
.footer-right {
  flex: 1 1 35%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.footer-right h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  text-decoration: none;
  color: #4b5563;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #111827;
  font-weight: 600;
}

.contact-info p {
  color: #4b5563;
  margin: 0.25rem 0;
  font-size: 0.95rem;
}

/* 📱 Mobile Responsiveness */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 3rem;
  }

  .name-fields {
    flex-direction: column; /* 👈 stack first name & last name vertically */
  }

  .contact-form button {
    width: 100%; /* 👈 button full width for mobile look */
    text-align: center;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/MetaStyleComponents/MetaHeader.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/* Meta-Inspired Header */
.MetaHeader_header__ZbNqr {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.MetaHeader_scrolled__EANpU {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.MetaHeader_container__xK6T0 {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* Logo */
.MetaHeader_logo__NmYEZ {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: #1c1e21;
  font-weight: 600;
  transition: opacity 0.2s ease;
}

.MetaHeader_logo__NmYEZ:hover {
  opacity: 0.8;
}

.MetaHeader_logoText__92n0c {
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: -0.01em;
}

/* Navigation */
.MetaHeader_nav__hJ1j7 {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.MetaHeader_navItem__auUlc {
  position: relative;
  display: flex;
  align-items: center;
}

.MetaHeader_navLink__JXTOX {
  color: #1c1e21;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.01em;
}

.MetaHeader_navLink__JXTOX:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #0866ff;
}

/* Dropdown */
.MetaHeader_dropdown__X1T3X {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  min-width: 220px;
  margin-top: 0.5rem;
}

.MetaHeader_dropdownItem__ApPdf {
  display: block;
  padding: 0.75rem 1rem;
  color: #1c1e21;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 400;
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 0 0.5rem;
}

.MetaHeader_dropdownItem__ApPdf:hover {
  background: rgba(8, 102, 255, 0.1);
  color: #0866ff;
}

/* Mobile Menu Button */
.MetaHeader_mobileMenuButton__Egs_n {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.MetaHeader_mobileMenuButton__Egs_n:hover {
  background: rgba(0, 0, 0, 0.05);
}

.MetaHeader_hamburgerLine__FCwvU {
  width: 20px;
  height: 2px;
  background: #1c1e21;
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.MetaHeader_hamburgerLine__FCwvU.MetaHeader_active__7Fexx:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.MetaHeader_hamburgerLine__FCwvU.MetaHeader_active__7Fexx:nth-child(2) {
  opacity: 0;
}

.MetaHeader_hamburgerLine__FCwvU.MetaHeader_active__7Fexx:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu */
.MetaHeader_mobileMenu__Kc_00 {
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 2rem 2rem;
  overflow: hidden;
}

.MetaHeader_mobileCategory__aQOf6 {
  margin-bottom: 1.5rem;
}

.MetaHeader_mobileCategoryTitle__ghVdJ {
  font-size: 0.875rem;
  font-weight: 600;
  color: #65676b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
}

.MetaHeader_mobileItems__MgXLv {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.MetaHeader_mobileItems__MgXLv .MetaHeader_dropdownItem__ApPdf {
  margin: 0;
  padding: 0.75rem 0;
  border-radius: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.MetaHeader_mobileItems__MgXLv .MetaHeader_dropdownItem__ApPdf:last-child {
  border-bottom: none;
}

/* Responsive */
@media (max-width: 768px) {
  .MetaHeader_container__xK6T0 {
    padding: 0 1rem;
  }
  
  .MetaHeader_nav__hJ1j7 {
    display: none;
  }
  
  .MetaHeader_mobileMenuButton__Egs_n {
    display: flex;
  }
  
  .MetaHeader_logoText__92n0c {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .MetaHeader_container__xK6T0 {
    height: 56px;
  }
  
  .MetaHeader_logoText__92n0c {
    display: none;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/MetaStyleComponents/MetaFooter.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/* Meta-Inspired Footer */
.MetaFooter_footer__pVpDf {
  background: #f8f9fa;
  border-top: 1px solid #e4e6ea;
  padding: 3rem 0 1rem;
  margin-top: 4rem;
}

.MetaFooter_container__4Ma_Z {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.MetaFooter_content__fvTia {
  width: 100%;
}

/* Main Content */
.MetaFooter_mainContent__njUct {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.MetaFooter_companySection__1f2QK {
  max-width: 400px;
}

.MetaFooter_companyName__WPiYO {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1c1e21;
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.MetaFooter_companyTagline__EAUBO {
  font-size: 1rem;
  font-weight: 600;
  color: #0866ff;
  margin-bottom: 1rem;
}

.MetaFooter_companyDescription__i6tOx {
  font-size: 0.875rem;
  color: #65676b;
  line-height: 1.5;
}

/* Links Grid */
.MetaFooter_linksGrid__30I6m {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.MetaFooter_linkSection__Isx5i {
  display: flex;
  flex-direction: column;
}

.MetaFooter_sectionTitle__BqHrQ {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1c1e21;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.MetaFooter_linkList__G3qsi {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.MetaFooter_footerLink__k05NU {
  color: #65676b;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 400;
  transition: color 0.2s ease;
  line-height: 1.4;
}

.MetaFooter_footerLink__k05NU:hover {
  color: #0866ff;
}

/* Bottom Bar */
.MetaFooter_bottomBar__RXjOx {
  border-top: 1px solid #e4e6ea;
  padding-top: 1.5rem;
  margin-top: 2rem;
}

.MetaFooter_bottomContent__WwmiF {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.MetaFooter_copyright__bQ7lL {
  color: #65676b;
  font-size: 0.8rem;
}

.MetaFooter_legalLinks__N_6HR {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.MetaFooter_legalLink__2V29R {
  color: #65676b;
  text-decoration: none;
  font-size: 0.8rem;
  transition: color 0.2s ease;
}

.MetaFooter_legalLink__2V29R:hover {
  color: #0866ff;
}

.MetaFooter_location__RsY7n {
  color: #65676b;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .MetaFooter_mainContent__njUct {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .MetaFooter_linksGrid__30I6m {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .MetaFooter_container__4Ma_Z {
    padding: 0 1rem;
  }
  
  .MetaFooter_footer__pVpDf {
    padding: 2rem 0 1rem;
  }
  
  .MetaFooter_linksGrid__30I6m {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .MetaFooter_bottomContent__WwmiF {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .MetaFooter_legalLinks__N_6HR {
    order: 2;
  }
  
  .MetaFooter_location__RsY7n {
    order: 3;
  }
}

@media (max-width: 480px) {
  .MetaFooter_legalLinks__N_6HR {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .MetaFooter_bottomContent__WwmiF {
    gap: 0.75rem;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/Transac/HeaderComponent/TransacHeader.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
.TransacHeader_header__AjiOP {
  background-color: #ffffff;
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 999;
  border-bottom: 1px solid #f3f4f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.TransacHeader_container__VZ7am {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.TransacHeader_logoWithIcon__BfTKB {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #1f2937;
}

.TransacHeader_logoText__uxLYp {
  font-size: 1.25rem;
  font-weight: 700;
}

.TransacHeader_navbar__FHJK2 {
  display: flex;
  align-items: center;
}

.TransacHeader_navLinks__q5D6E {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.TransacHeader_navItem__IoYtz {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
}

.TransacHeader_navItem__IoYtz:hover {
  color: #111827;
}

.TransacHeader_navItemWrapper__MmGeO {
  position: relative;
}

.TransacHeader_dropdown__PwmN4 {
  position: absolute;
  top: 2.5rem;
  left: 0;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 200px;
  z-index: 1000;
}

.TransacHeader_dropdownItem__V2nwY {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  text-decoration: none;
  color: #4b5563;
}

.TransacHeader_dropdownItem__V2nwY:hover {
  background-color: #facc15;
  color: #1f2937;
}

/* .ctaButton {
  background-color: #facc15;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: #1f2937;
} */

.TransacHeader_signInPrompt__0RdHn {
  font-size: 0.85rem;
  color: #6b7280;
}
.TransacHeader_ctaButton__EJ9QC {
  background-color: #facc15;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: #1f2937;
  text-decoration: none; /* 🔥 remove underline */
}

.TransacHeader_signInLink__ohniV {
  margin-left: 0.25rem;
  color: #1f2937;
  font-weight: 500;
  text-decoration: underline;
}

/* 🔥 Mobile Styles */
.TransacHeader_mobileMenuButton__IYxxr {
  display: none;
  background: none;
  border: none;
  font-size: 1.75rem;
  color: #1f2937;
  margin-left: auto;
  padding-right: 3rem;
}

.TransacHeader_mobileMenu__Nv6KS {
  position: fixed;
  top: 0;
  right: 0;
  background: white;
  width: 80%;
  height: 100%;
  z-index: 2000;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  animation: TransacHeader_slideIn__JqSzc 0.3s ease-in-out;
}

.TransacHeader_menuSlide__5ZUsI {
  animation: TransacHeader_slideOut__O5DiR 0.3s ease-in-out reverse;
}

@keyframes TransacHeader_slideIn__JqSzc {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}

@keyframes TransacHeader_slideOut__O5DiR {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}

.TransacHeader_closeBtn__rTHVm {
  align-self: flex-end;
  font-size: 2rem;
  background: none;
  border: none;
  cursor: pointer;
}

.TransacHeader_mobileDropdownWrapper__S3VM7 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.TransacHeader_mobileDropdownToggle__8NR8e {
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  cursor: pointer;
  text-align: left;
  padding: 0;
}

.TransacHeader_mobileDropdown__rMLRI {
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .TransacHeader_mobileMenuButton__IYxxr {
    display: block;
  }

  .TransacHeader_navLinks__q5D6E {
    display: none;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/components/FrontendWebDesign/HeaderComponent/HeaderComponent.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
.cuberto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: white;
  color: black;
  position: relative;
  z-index: 50;
  font-family: 'Segoe UI', sans-serif;
  box-sizing: border-box;
}

.menu-button {
  background: none;
  color: black;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: black;
}

.hamburger {
  font-size: 1.3rem;
}

.menu-overlay {
  position: fixed;
  right: 0;
  top: 0;
  width: 50vw;
  max-width: 400px;
  height: 100vh;
  background: white;
  color: black;
  padding: 3rem 2rem;
  display: flex;
  flex-direction: column;
  z-index: 100;
  animation: slideIn 0.5s ease forwards;
  box-sizing: border-box;
  overflow-y: auto;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}

.close-button {
  font-size: 2rem;
  border: none;
  background: none;
  align-self: flex-end;
  cursor: pointer;
}

.menu-content {
  display: flex;
  flex: 1;
  margin-top: 2rem;
  justify-content: space-between;
  gap: 2rem;
}

.menu-left ul,
.menu-right ul {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.menu-left li,
.menu-right li {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.menu-right li:hover {
  transform: scale(1.2);
}

.menu-heading {
  font-size: 0.85rem;
  font-weight: 500;
  color: gray;
  margin-bottom: 0.5rem;
}

.contact-block {
  margin-top: 2rem;
  font-size: 0.9rem;
}

/* ✅ Make links look like plain text */
.menu-content a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

.menu-content a:hover {
  color: #888;
}

/* ✅ Hide bullets */
.menu-content li {
  list-style: none;
}

.cursor-circle {
  position: fixed;
  width: 50px;
  height: 50px;
  border-radius: 999px;
  background: black;
  pointer-events: none;
  mix-blend-mode: difference;
  z-index: 200;
  transition: all 0.1s ease;
}

/* ✅ Mobile Responsive */
@media (max-width: 768px) {
  .menu-overlay {
    width: 100vw;
    max-width: 100vw;
    padding: 2rem 1.5rem;
  }

  .menu-content {
    flex-direction: column;
  }

  .menu-left,
  .menu-right {
    width: 100%;
  }

  .cursor-circle {
    display: none;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/SavourAndSip/PageAnimationComponent/PageAnimation.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
.PageAnimation_loader__b8dp4 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: black;
    color: #cba135; /* Golden */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: PageAnimation_fadeOut__0qT_d 1s ease 2.4s forwards;
  }
  
  .PageAnimation_loader__b8dp4 h1 {
    font-family: 'Georgia', serif;
    font-size: 36px;
    letter-spacing: 2px;
    opacity: 1;
    animation: PageAnimation_fadeIn__brgQZ 1s ease forwards;
  }
  
  @keyframes PageAnimation_fadeIn__brgQZ {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes PageAnimation_fadeOut__0qT_d {
    to {
      opacity: 0;
      visibility: hidden;
    }
  }
  
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.js","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
  font-family: 'Geist Fallback';
  src: local("Arial");
  ascent-override:95.94%;
  descent-override:28.16%;
  line-gap-override:0.00%;
  size-adjust:104.76%;
}@font-face {font-family: 'Geist Fallback Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_f27602 {font-family: 'Geist Fallback', 'Geist Fallback Fallback';font-style: normal
}.__variable_f27602 {--font-geist-sans: 'Geist Fallback', 'Geist Fallback Fallback'
}
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.js","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
  font-family: 'Geist Mono Fallback';
  src: local("Arial");
  ascent-override:74.67%;
  descent-override:21.92%;
  line-gap-override:0.00%;
  size-adjust:134.59%;
}@font-face {font-family: 'Geist Mono Fallback Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_22812c {font-family: 'Geist Mono Fallback', 'Geist Mono Fallback Fallback';font-style: normal
}.__variable_22812c {--font-geist-mono: 'Geist Mono Fallback', 'Geist Mono Fallback Fallback'
}
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
@layer properties;
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.ml-auto {
  margin-left: auto;
}
.flex {
  display: flex;
}
.hidden {
  display: none;
}
.h-full {
  height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.w-full {
  width: 100%;
}
.flex-1 {
  flex: 1;
}
.grow {
  flex-grow: 1;
}
.rotate-90 {
  rotate: 90deg;
}
.transform {
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.cursor-pointer {
  cursor: pointer;
}
.resize {
  resize: both;
}
.flex-col {
  flex-direction: column;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
:root {
  --background: #0B0F19;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0B0F19;
    --foreground: rgb(0, 0, 0);
  }
}
html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  font-family: Arial, Helvetica, sans-serif;
  background-color: var(--background);
  color: var(--foreground);
}
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  background-color: #1f2937;
  color: white;
  border: 1px solid #374151;
}
.submit-btn {
  width: 100%;
  padding: 1rem;
  background-color: #4f46e5;
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}
.submit-btn:hover {
  background-color: #4338ca;
}
html {
  scroll-behavior: smooth;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-outline-style: solid;
    }
  }
}

