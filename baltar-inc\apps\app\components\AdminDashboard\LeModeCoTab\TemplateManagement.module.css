/* Template Management Styles */
.templateManagement {
  padding: 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.createBtn {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.createBtn:hover {
  background: #b8941f;
}

.filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.filterSelect {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  flex: 1;
  min-width: 200px;
}

.filterSelect:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.templatesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.templateCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s;
}

.templateCard:hover {
  transform: translateY(-2px);
}

.templateCard.inactive {
  opacity: 0.6;
  border: 2px dashed #e2e8f0;
}

.templateHeader {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.templateHeader h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.templateMeta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.templateType,
.packageTier,
.season {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.templateType {
  background: #e6fffa;
  color: #234e52;
}

.packageTier {
  background: #fef5e7;
  color: #744210;
}

.season {
  background: #e6f3ff;
  color: #1a365d;
}

.templateDescription {
  padding: 1rem 1.5rem;
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.5;
}

.templateItems {
  padding: 1rem 1.5rem;
}

.templateItems h5 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1rem;
}

.itemsPreview {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.itemPreview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f7fafc;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  color: #4a5568;
  max-width: 150px;
}

.itemPreview img {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
}

.itemPreview span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.moreItems {
  background: #e2e8f0;
  color: #4a5568;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
}

.templateActions {
  display: flex;
  gap: 0.5rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.editBtn {
  flex: 1;
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.editBtn:hover {
  background: #3182ce;
}

.duplicateBtn {
  flex: 1;
  padding: 0.5rem 1rem;
  background: #9f7aea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.duplicateBtn:hover {
  background: #805ad5;
}

.toggleBtn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.toggleBtn.active {
  background: #f56565;
  color: white;
}

.toggleBtn.active:hover {
  background: #e53e3e;
}

.toggleBtn.inactive {
  background: #48bb78;
  color: white;
}

.toggleBtn.inactive:hover {
  background: #38a169;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modalHeader h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.closeBtn:hover {
  background: #f7fafc;
}

.modalForm {
  padding: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.cancelBtn {
  padding: 0.75rem 1.5rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.cancelBtn:hover {
  background: #cbd5e0;
}

.saveBtn {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.saveBtn:hover {
  background: #b8941f;
}

/* Enhanced Template Modal Styles */
.productSection {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.sectionHeader h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1rem;
}

.addProductBtn {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.addProductBtn:hover {
  background: #3182ce;
}

.selectedProducts {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.selectedProduct {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  position: relative;
}

.selectedProduct .productImage {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.selectedProduct .productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selectedProduct .productInfo {
  flex: 1;
}

.selectedProduct .productInfo h5 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.selectedProduct .productInfo p {
  margin: 0;
  color: #718096;
  font-size: 0.75rem;
}

.selectedProduct .price {
  color: #d4af37;
  font-weight: 600;
}

.quantityControl {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantityControl label {
  margin: 0;
  color: #4a5568;
  font-size: 0.75rem;
}

.quantityControl input {
  width: 60px;
  padding: 0.25rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.75rem;
}

.removeProductBtn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background 0.2s;
}

.removeProductBtn:hover {
  background: #e53e3e;
}

.productSelector {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  background: #f7fafc;
}

.productFilters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.searchInput {
  flex: 2;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.filterSelect {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.availableProducts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.availableProduct {
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem;
  transition: border-color 0.2s;
}

.availableProduct:hover {
  border-color: #d4af37;
}

.availableProduct .productImage {
  width: 100%;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.availableProduct .productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.availableProduct .productInfo h5 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 0.875rem;
  line-height: 1.2;
}

.availableProduct .productInfo p {
  margin: 0 0 0.25rem 0;
  color: #718096;
  font-size: 0.75rem;
}

.availableProduct .price {
  color: #d4af37;
  font-weight: 600;
}

.availableProduct .stock {
  color: #4a5568;
}

.availableProduct .addBtn {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background 0.2s;
}

.availableProduct .addBtn:hover {
  background: #38a169;
}

.noProducts {
  grid-column: 1 / -1;
  text-align: center;
  color: #718096;
  padding: 2rem;
  font-style: italic;
}

.noImage {
  color: #a0aec0;
  font-size: 0.75rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
  
  .filterSelect {
    min-width: auto;
  }
  
  .templatesGrid {
    grid-template-columns: 1fr;
  }
  
  .templateActions {
    flex-direction: column;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .modal {
    width: 95%;
    margin: 1rem;
  }
  
  .modalActions {
    flex-direction: column;
  }
}
