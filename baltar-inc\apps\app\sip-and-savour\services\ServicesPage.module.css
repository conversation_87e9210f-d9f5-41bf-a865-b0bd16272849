.servicesPage {
  min-height: 100vh;
  background: #ffffff;
}

/* Navigation styles removed - using SharedHeader component */

/* Hero Section */
.heroSection {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.heroVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.7), rgba(45, 45, 45, 0.5));
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  color: #d4af37;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #cccccc;
  line-height: 1.6;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Services Section */
.servicesSection {
  padding: 6rem 0;
  background: #ffffff;
}

.serviceCard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  margin-bottom: 4rem;
  min-height: 500px;
}

.serviceCard:nth-child(even) {
  grid-template-columns: 1fr 1fr;
}

.serviceCard:nth-child(even) .serviceMedia {
  order: 2;
}

.serviceCard:nth-child(even) .serviceContent {
  order: 1;
}

.serviceMedia {
  position: relative;
  overflow: hidden;
}

.serviceVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.serviceCard:hover .serviceVideo {
  transform: scale(1.05);
}

.serviceIcon {
  position: absolute;
  top: 2rem;
  left: 2rem;
  font-size: 3rem;
  background: rgba(212, 175, 55, 0.9);
  padding: 1rem;
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.serviceContent {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.serviceTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.serviceDescription {
  font-size: 1.1rem;
  color: #4a4a4a;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.serviceDetails {
  margin-bottom: 2rem;
}

.detailSection {
  margin-bottom: 1.5rem;
}

.detailSection h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.detailSection ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.detailSection li {
  font-size: 0.95rem;
  color: #4a4a4a;
  padding: 0.25rem 0;
  padding-left: 1rem;
  position: relative;
}

.detailSection li::before {
  content: '•';
  color: #d4af37;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.serviceButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.serviceButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  color: #d4af37;
}

.ctaDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  color: #cccccc;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid #d4af37;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  background: #d4af37;
  color: #1a1a1a;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .serviceCard,
  .serviceCard:nth-child(even) {
    grid-template-columns: 1fr;
  }

  .serviceCard:nth-child(even) .serviceMedia,
  .serviceCard:nth-child(even) .serviceContent {
    order: initial;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .serviceContent {
    padding: 2rem;
  }
  
  .serviceTitle {
    font-size: 1.75rem;
  }
  
  .serviceDescription {
    font-size: 1rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .serviceContent {
    padding: 1.5rem;
  }
  
  .serviceTitle {
    font-size: 1.5rem;
  }
  
  .container {
    padding: 0 1rem;
  }
}
