/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/ConsumerPulseTab/ConsumerPulseTab.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
.ConsumerPulseTab_consumerPulseTab__lNtxO {
  padding: 2rem;
  background: #f8f9fa;
  min-height: 100vh;
}

.ConsumerPulseTab_header__yHCyC {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #dee2e6;
}

.ConsumerPulseTab_header__yHCyC h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.ConsumerPulseTab_controls__SeJhS {
  display: flex;
  gap: 1rem;
}

.ConsumerPulseTab_controlGroup__HBhMb {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ConsumerPulseTab_controlGroup__HBhMb h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.ConsumerPulseTab_schedulerStatus__nChvG {
  background: #f8f9fa;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.ConsumerPulseTab_schedulerStatus__nChvG p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #495057;
}

.ConsumerPulseTab_running__YK8C8 {
  color: #28a745;
  font-weight: 600;
}

.ConsumerPulseTab_stopped__eWzWH {
  color: #dc3545;
  font-weight: 600;
}

.ConsumerPulseTab_scrapeButton__yp4kH, .ConsumerPulseTab_autoButton__qOAnL {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ConsumerPulseTab_scrapeButton__yp4kH {
  background: #007bff;
  color: white;
}

.ConsumerPulseTab_scrapeButton__yp4kH:hover:not(:disabled) {
  background: #0056b3;
}

.ConsumerPulseTab_scrapeButton__yp4kH:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.ConsumerPulseTab_autoButton__qOAnL {
  background: #28a745;
  color: white;
}

.ConsumerPulseTab_autoButton__qOAnL:hover:not(:disabled) {
  background: #1e7e34;
}

.ConsumerPulseTab_autoButton__qOAnL:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.ConsumerPulseTab_testButton__Quv_X, .ConsumerPulseTab_newsDataButton__QTPdP, .ConsumerPulseTab_startButton__ncxs0, .ConsumerPulseTab_stopButton__uAYCz {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.ConsumerPulseTab_testButton__Quv_X {
  background: #17a2b8;
  color: white;
}

.ConsumerPulseTab_testButton__Quv_X:hover {
  background: #138496;
}

.ConsumerPulseTab_newsDataButton__QTPdP {
  background: #007bff;
  color: white;
}

.ConsumerPulseTab_newsDataButton__QTPdP:hover:not(:disabled) {
  background: #0056b3;
}

.ConsumerPulseTab_newsDataButton__QTPdP:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.ConsumerPulseTab_startButton__ncxs0 {
  background: #28a745;
  color: white;
}

.ConsumerPulseTab_startButton__ncxs0:hover {
  background: #1e7e34;
}

.ConsumerPulseTab_stopButton__uAYCz {
  background: #dc3545;
  color: white;
}

.ConsumerPulseTab_stopButton__uAYCz:hover {
  background: #c82333;
}

.ConsumerPulseTab_statsGrid__cL0MH {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.ConsumerPulseTab_statCard__Bx48Y {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.ConsumerPulseTab_statCard__Bx48Y h3 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
}

.ConsumerPulseTab_statNumber__xPOzk {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.ConsumerPulseTab_statLabel__ibIG5 {
  color: #6c757d;
  font-size: 0.9rem;
}

.ConsumerPulseTab_articlesSection__tDLP_ {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ConsumerPulseTab_articlesSection__tDLP_ h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.ConsumerPulseTab_articlesList__jT2eg {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ConsumerPulseTab_articleCard__oA1Ia {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #f8f9fa;
}

.ConsumerPulseTab_articleInfo__QFZsR {
  flex: 1;
  margin-right: 1rem;
}

.ConsumerPulseTab_articleTitle__VSdR1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.ConsumerPulseTab_articleSummary__fZUZ3 {
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.4;
}

.ConsumerPulseTab_articleMeta__hIBZi {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.ConsumerPulseTab_status__J_Vo9 {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.ConsumerPulseTab_status__J_Vo9.ConsumerPulseTab_draft__K227T {
  background: #ffc107;
  color: #212529;
}

.ConsumerPulseTab_status__J_Vo9.ConsumerPulseTab_published__ekgv5 {
  background: #28a745;
  color: white;
}

.ConsumerPulseTab_sentiment__GbBfw {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.ConsumerPulseTab_sentiment__GbBfw.ConsumerPulseTab_positive__pvBTZ {
  background: #d4edda;
  color: #155724;
}

.ConsumerPulseTab_sentiment__GbBfw.ConsumerPulseTab_negative__cvdY6 {
  background: #f8d7da;
  color: #721c24;
}

.ConsumerPulseTab_sentiment__GbBfw.ConsumerPulseTab_neutral__goI4y {
  background: #e2e3e5;
  color: #383d41;
}

.ConsumerPulseTab_date__dwCzr {
  color: #6c757d;
  font-size: 0.8rem;
}

.ConsumerPulseTab_articleActions__Fj6Ti {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ConsumerPulseTab_publishButton__yQ1jW, .ConsumerPulseTab_viewButton__OWtQX {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ConsumerPulseTab_publishButton__yQ1jW {
  background: #28a745;
  color: white;
}

.ConsumerPulseTab_publishButton__yQ1jW:hover {
  background: #1e7e34;
}

.ConsumerPulseTab_viewButton__OWtQX {
  background: #6c757d;
  color: white;
}

.ConsumerPulseTab_viewButton__OWtQX:hover {
  background: #545b62;
}

.ConsumerPulseTab_loadingContainer__hY4UU {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.ConsumerPulseTab_loadingSpinner__vcQIy {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: ConsumerPulseTab_spin__qHmEN 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes ConsumerPulseTab_spin__qHmEN {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .ConsumerPulseTab_consumerPulseTab__lNtxO {
    padding: 1rem;
  }
  
  .ConsumerPulseTab_header__yHCyC {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .ConsumerPulseTab_controls__SeJhS {
    justify-content: center;
  }
  
  .ConsumerPulseTab_articleCard__oA1Ia {
    flex-direction: column;
    gap: 1rem;
  }
  
  .ConsumerPulseTab_articleInfo__QFZsR {
    margin-right: 0;
  }
  
  .ConsumerPulseTab_articleActions__Fj6Ti {
    flex-direction: row;
    justify-content: flex-end;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/ProductManagement.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Product Management Styles */
.ProductManagement_productManagement__oZF_p {
  padding: 0;
}

.ProductManagement_loadingContainer__sLqOS {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.ProductManagement_loadingSpinner__TxDuP {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: ProductManagement_spin__xnQuK 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes ProductManagement_spin__xnQuK {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ProductManagement_header__DWChU {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ProductManagement_header__DWChU h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.ProductManagement_createBtn__1CABu {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.ProductManagement_createBtn__1CABu:hover {
  background: #b8941f;
}

.ProductManagement_filters__omquh {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.ProductManagement_searchInput__Q7JMJ,
.ProductManagement_filterSelect__PgtJj,
.ProductManagement_brandInput__9qZHu {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  flex: 1;
  min-width: 200px;
}

.ProductManagement_searchInput__Q7JMJ:focus,
.ProductManagement_filterSelect__PgtJj:focus,
.ProductManagement_brandInput__9qZHu:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.ProductManagement_productsGrid__5ujSB {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.ProductManagement_productCard__jRC0K {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s;
}

.ProductManagement_productCard__jRC0K:hover {
  transform: translateY(-2px);
}

.ProductManagement_productCard__jRC0K.ProductManagement_inactive__Qyi5o {
  opacity: 0.6;
  border: 2px dashed #e2e8f0;
}

.ProductManagement_productImage__HO9ML {
  height: 200px;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.ProductManagement_productImage__HO9ML img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.ProductManagement_noImage__0M_Q4 {
  color: #a0aec0;
  font-size: 0.875rem;
}

.ProductManagement_productInfo__pxcw7 {
  padding: 1rem;
}

.ProductManagement_productInfo__pxcw7 h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.ProductManagement_brand__jOU_M {
  color: #718096;
  font-size: 0.875rem;
  margin: 0 0 0.25rem 0;
  font-weight: 500;
}

.ProductManagement_category__oYPkb {
  color: #4a5568;
  font-size: 0.75rem;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #edf2f7;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

.ProductManagement_price__JILQx {
  color: #d4af37;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.ProductManagement_stock__DaNcR {
  color: #4a5568;
  font-size: 0.875rem;
  margin: 0;
}

.ProductManagement_productActions__qsNRx {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
}

.ProductManagement_editBtn__N5S3I {
  flex: 1;
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.ProductManagement_editBtn__N5S3I:hover {
  background: #3182ce;
}

.ProductManagement_toggleBtn__yJSVd {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.ProductManagement_toggleBtn__yJSVd.ProductManagement_active__01Rnb {
  background: #f56565;
  color: white;
}

.ProductManagement_toggleBtn__yJSVd.ProductManagement_active__01Rnb:hover {
  background: #e53e3e;
}

.ProductManagement_toggleBtn__yJSVd.ProductManagement_inactive__Qyi5o {
  background: #48bb78;
  color: white;
}

.ProductManagement_toggleBtn__yJSVd.ProductManagement_inactive__Qyi5o:hover {
  background: #38a169;
}

/* Modal Styles */
.ProductManagement_modalOverlay__hxwRl {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.ProductManagement_modal__y_LId {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.ProductManagement_modalHeader__HjYvJ {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.ProductManagement_modalHeader__HjYvJ h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.ProductManagement_closeBtn__LyLsu {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.ProductManagement_closeBtn__LyLsu:hover {
  background: #f7fafc;
}

.ProductManagement_modalForm__SNWmQ {
  padding: 1.5rem;
}

.ProductManagement_formRow___QguQ {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.ProductManagement_formGroup__EHBaA {
  margin-bottom: 1.5rem;
}

.ProductManagement_formGroup__EHBaA label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.ProductManagement_formGroup__EHBaA input,
.ProductManagement_formGroup__EHBaA textarea,
.ProductManagement_formGroup__EHBaA select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.ProductManagement_formGroup__EHBaA input:focus,
.ProductManagement_formGroup__EHBaA textarea:focus,
.ProductManagement_formGroup__EHBaA select:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.ProductManagement_fileInput__eu1rv {
  border: 2px dashed #e2e8f0 !important;
  padding: 1rem !important;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.ProductManagement_fileInput__eu1rv:hover {
  border-color: #d4af37 !important;
}

.ProductManagement_fileHint__2JRW_ {
  margin: 0.5rem 0 0 0;
  color: #718096;
  font-size: 0.75rem;
}

.ProductManagement_modalActions__xR66o {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.ProductManagement_cancelBtn__d1_G0 {
  padding: 0.75rem 1.5rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.ProductManagement_cancelBtn__d1_G0:hover {
  background: #cbd5e0;
}

.ProductManagement_saveBtn__OrFgk {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.ProductManagement_saveBtn__OrFgk:hover:not(:disabled) {
  background: #b8941f;
}

.ProductManagement_saveBtn__OrFgk:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ProductManagement_filters__omquh {
    flex-direction: column;
  }
  
  .ProductManagement_searchInput__Q7JMJ,
  .ProductManagement_filterSelect__PgtJj,
  .ProductManagement_brandInput__9qZHu {
    min-width: auto;
  }
  
  .ProductManagement_productsGrid__5ujSB {
    grid-template-columns: 1fr;
  }
  
  .ProductManagement_formRow___QguQ {
    grid-template-columns: 1fr;
  }
  
  .ProductManagement_modal__y_LId {
    width: 95%;
    margin: 1rem;
  }
  
  .ProductManagement_modalActions__xR66o {
    flex-direction: column;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/TemplateApplyModal.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.TemplateApplyModal_modalOverlay__gd_ig {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  animation: TemplateApplyModal_fadeIn__0Ntqa 0.3s ease-out;
}

@keyframes TemplateApplyModal_fadeIn__0Ntqa {
  from { opacity: 0; }
  to { opacity: 1; }
}

.TemplateApplyModal_modal__Veh1L {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  max-width: 800px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: TemplateApplyModal_slideUp__Pya2O 0.3s ease-out;
}

@keyframes TemplateApplyModal_slideUp__Pya2O {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.TemplateApplyModal_modalHeader__9lNgC {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.TemplateApplyModal_modalHeader__9lNgC h3 {
  margin: 0;
  color: #111827;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.TemplateApplyModal_closeButton__pH208 {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  color: #6b7280;
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.TemplateApplyModal_closeButton__pH208:hover {
  background: #e2e8f0;
  color: #334155;
}

.TemplateApplyModal_modalBody__P6tUq {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem 2rem;
}

.TemplateApplyModal_loading__xzXXS {
  text-align: center;
  padding: 3rem;
  color: #64748b;
  font-size: 1.125rem;
}

.TemplateApplyModal_emptyState__WjOBR {
  text-align: center;
  padding: 3rem;
}

.TemplateApplyModal_emptyState__WjOBR h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.25rem;
}

.TemplateApplyModal_emptyState__WjOBR p {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}

.TemplateApplyModal_templatesGrid__U8E9b {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.TemplateApplyModal_templateCard__qSJdw {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background: #fafafa;
}

.TemplateApplyModal_templateCard__qSJdw:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.TemplateApplyModal_templateCard__qSJdw.TemplateApplyModal_selected__987Ix {
  border-color: #10b981;
  background: #f0fdf4;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.TemplateApplyModal_templateHeader__VJ0hQ {
  margin-bottom: 1rem;
}

.TemplateApplyModal_templateHeader__VJ0hQ h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 700;
}

.TemplateApplyModal_templateMeta__6_t8Z {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.TemplateApplyModal_itemCount__UVXGV {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.TemplateApplyModal_templateValue__mK2Pq {
  color: #059669;
  font-weight: 700;
  font-size: 1rem;
}

.TemplateApplyModal_templateDescription__WqN1Q {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  font-style: italic;
}

.TemplateApplyModal_templateItems__vmvo1 h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.TemplateApplyModal_itemsList__73TD6 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.TemplateApplyModal_templateItem__YxnwX {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.TemplateApplyModal_itemName__jZF_5 {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.TemplateApplyModal_itemQuantity___1DRJ {
  color: #6b7280;
  font-size: 0.75rem;
  background: #f3f4f6;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
}

.TemplateApplyModal_moreItems__zJILr {
  color: #6b7280;
  font-size: 0.75rem;
  text-align: center;
  padding: 0.5rem;
  font-style: italic;
}

.TemplateApplyModal_selectedBadge__d2NM0 {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #10b981;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.TemplateApplyModal_selectedTemplate__bzLKB {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.TemplateApplyModal_selectedTemplate__bzLKB h4 {
  margin: 0 0 0.5rem 0;
  color: #0369a1;
  font-size: 1rem;
}

.TemplateApplyModal_selectedTemplate__bzLKB p {
  margin: 0 0 0.75rem 0;
  color: #0369a1;
}

.TemplateApplyModal_warningNote__hb57j {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #92400e;
}

.TemplateApplyModal_warningNote__hb57j strong {
  font-weight: 600;
}

.TemplateApplyModal_modalFooter__RT8Qu {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.TemplateApplyModal_cancelButton__H28f8 {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.TemplateApplyModal_cancelButton__H28f8:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.TemplateApplyModal_applyButton__qH1B_ {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.TemplateApplyModal_applyButton__qH1B_:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.TemplateApplyModal_applyButton__qH1B_:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/ProductSearchModal.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
.ProductSearchModal_modalOverlay__UbOJR {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  animation: ProductSearchModal_fadeIn__ubBoP 0.3s ease-out;
  pointer-events: auto;
}

@keyframes ProductSearchModal_fadeIn__ubBoP {
  from { opacity: 0; }
  to { opacity: 1; }
}

.ProductSearchModal_modal__mNoX_ {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  width: 1200px;
  max-width: 95vw;
  height: 80vh;
  max-height: 800px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: ProductSearchModal_slideUp__ib3Xr 0.3s ease-out;
  position: relative;
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: auto;
}

@keyframes ProductSearchModal_slideUp__ib3Xr {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ProductSearchModal_modalHeader__wYGvK {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.ProductSearchModal_modalHeader__wYGvK h3 {
  margin: 0;
  color: #111827;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.ProductSearchModal_closeButton__lsnnS {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  cursor: pointer;
  padding: 8px;
  border-radius: 10px;
  color: #6b7280;
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.ProductSearchModal_closeButton__lsnnS:hover {
  background: #e2e8f0;
  color: #334155;
}

.ProductSearchModal_modalBody__f0nbx {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem 2rem;
  max-width: 100%;
  box-sizing: border-box;
}

.ProductSearchModal_searchSection__dlMU9 {
  margin-bottom: 1.5rem;
}

.ProductSearchModal_searchInput__lFAYh {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.ProductSearchModal_searchInput__lFAYh:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ProductSearchModal_selectedSection__S2KsP {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.ProductSearchModal_selectedSection__S2KsP h4 {
  margin: 0 0 1rem 0;
  color: #0369a1;
  font-size: 1rem;
}

.ProductSearchModal_selectedProducts__gYfQB {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ProductSearchModal_selectedProduct__4ZmMs {
  background: white;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ProductSearchModal_productInfo__LRDZn {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ProductSearchModal_productName__0uYML {
  font-weight: 600;
  color: #1e293b;
}

.ProductSearchModal_productPrice__q93_H {
  color: #059669;
  font-weight: 600;
}

.ProductSearchModal_quantityControl__OCMhG {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f1f5f9;
  border-radius: 6px;
  padding: 0.25rem;
}

.ProductSearchModal_quantityControl__OCMhG button {
  background: #3b82f6;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ProductSearchModal_quantityControl__OCMhG button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.ProductSearchModal_quantityControl__OCMhG span {
  min-width: 20px;
  text-align: center;
  font-weight: 600;
}

.ProductSearchModal_removeSelected__BNeUL {
  background: #fef2f2;
  color: #dc2626;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.875rem;
}

.ProductSearchModal_removeSelected__BNeUL:hover {
  background: #fee2e2;
}

.ProductSearchModal_productsSection__bjXTO h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1rem;
}

.ProductSearchModal_loading__rkGNv {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.ProductSearchModal_productsList__QV_HL {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
  max-width: 100%;
  box-sizing: border-box;
}

.ProductSearchModal_productCard__z2dpm {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
  min-width: 0;
}

.ProductSearchModal_productCard__z2dpm:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.ProductSearchModal_productCard__z2dpm.ProductSearchModal_selected__RH3vw {
  border-color: #10b981;
  background: #f0fdf4;
}

.ProductSearchModal_productHeader__GBIKD {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.ProductSearchModal_productHeader__GBIKD h5 {
  margin: 0;
  color: #1e293b;
  font-size: 0.875rem;
  font-weight: 600;
  flex: 1;
}

.ProductSearchModal_price__Qe1xC {
  color: #059669;
  font-weight: 700;
  font-size: 0.875rem;
}

.ProductSearchModal_productDetails__wFqY6 {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.ProductSearchModal_productDetails__wFqY6 span {
  font-size: 0.75rem;
  color: #64748b;
}

.ProductSearchModal_productStock__RGKrU {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.ProductSearchModal_selectedBadge__uwGoM {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #10b981;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.ProductSearchModal_modalFooter__Z4H2i {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.ProductSearchModal_cancelButton__zpS1B {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.ProductSearchModal_cancelButton__zpS1B:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.ProductSearchModal_addButton__L8RVE {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.ProductSearchModal_addButton__L8RVE:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.ProductSearchModal_addButton__L8RVE:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design - Prevent Modal Switching */
@media (max-width: 768px) {
  .ProductSearchModal_modal__mNoX_ {
    width: 95vw !important;
    height: 90vh !important;
    max-height: 90vh !important;
  }

  .ProductSearchModal_productsList__QV_HL {
    grid-template-columns: 1fr !important;
    gap: 0.75rem;
  }

  .ProductSearchModal_productCard__z2dpm {
    padding: 0.75rem;
  }
}

@media (min-width: 769px) {
  .ProductSearchModal_modal__mNoX_ {
    width: 1200px !important;
    max-width: 95vw !important;
  }

  .ProductSearchModal_productsList__QV_HL {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  }
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/EnhancedOrderManagement.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Enhanced Order Management Styles - Professional Design */
.EnhancedOrderManagement_orderManagement__5wqvS {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 32px;
  background: #f8fafc;
  min-height: 100vh;
}

.EnhancedOrderManagement_header__ePESN {
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.EnhancedOrderManagement_headerLeft__4_5Pe {
  flex: 1;
}

.EnhancedOrderManagement_headerActions__k9C5F {
  display: flex;
  gap: 16px;
  align-items: center;
}

.EnhancedOrderManagement_header__ePESN h3 {
  margin: 0 0 12px 0;
  color: #111827;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.EnhancedOrderManagement_customerDetails__PL8qV {
  display: flex;
  gap: 16px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.EnhancedOrderManagement_customerDetails__PL8qV span {
  background: #f3f4f6;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.EnhancedOrderManagement_statusBadge__PA8_v {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.EnhancedOrderManagement_statusBadge__PA8_v.EnhancedOrderManagement_pending__SowOo {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.EnhancedOrderManagement_statusBadge__PA8_v.EnhancedOrderManagement_paid__ATRRm {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.EnhancedOrderManagement_statusBadge__PA8_v.EnhancedOrderManagement_failed__A7pTj {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.EnhancedOrderManagement_statusBadge__PA8_v.EnhancedOrderManagement_complimentary__EhNf3 {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.EnhancedOrderManagement_statusBadge__PA8_v.EnhancedOrderManagement_cancelled__JiDAQ {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

.EnhancedOrderManagement_createOrderBtn__88mzy {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

.EnhancedOrderManagement_createOrderBtn__88mzy:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.35);
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.EnhancedOrderManagement_createOrderBtn__88mzy:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Empty Orders State */
.EnhancedOrderManagement_emptyOrders__ibw_V {
  background: white;
  border-radius: 16px;
  padding: 64px 48px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.EnhancedOrderManagement_emptyOrdersContent__w4yfU h4 {
  margin: 0 0 16px 0;
  color: #111827;
  font-size: 24px;
  font-weight: 700;
}

.EnhancedOrderManagement_emptyOrdersContent__w4yfU p {
  margin: 0 0 32px 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
}

.EnhancedOrderManagement_createFirstOrderBtn__QS8Pp {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
}

.EnhancedOrderManagement_createFirstOrderBtn__QS8Pp:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.35);
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.EnhancedOrderManagement_createFirstOrderBtn__QS8Pp:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Enhanced Order Card Styles */
.EnhancedOrderManagement_orderCard__AtYst {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.EnhancedOrderManagement_orderCard__AtYst:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.EnhancedOrderManagement_orderHeader__cppKj {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid #f3f4f6;
}

.EnhancedOrderManagement_orderHeaderLeft__vaZdv h4 {
  margin: 0 0 8px 0;
  color: #111827;
  font-size: 20px;
  font-weight: 700;
}

.EnhancedOrderManagement_orderDate__prcXk {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  font-weight: 500;
}

.EnhancedOrderManagement_orderHeaderRight__4sbXv {
  display: flex;
  align-items: center;
  gap: 16px;
}

.EnhancedOrderManagement_statusSelect__r1iYC {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  background: white;
  color: #374151;
  cursor: pointer;
  min-width: 140px;
  transition: all 0.2s ease;
}

.EnhancedOrderManagement_statusSelect__r1iYC:hover {
  border-color: #9ca3af;
}

.EnhancedOrderManagement_statusSelect__r1iYC:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.EnhancedOrderManagement_expandBtn__A2_0B {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.EnhancedOrderManagement_expandBtn__A2_0B:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* Order Summary */
.EnhancedOrderManagement_orderSummary__vs3tc {
  display: flex;
  gap: 1.5rem;
  padding: 1rem 0;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.EnhancedOrderManagement_summaryItem__L8B8K {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.EnhancedOrderManagement_summaryLabel__FOY5I {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  font-weight: 600;
}

.EnhancedOrderManagement_summaryValue__90KNL {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.EnhancedOrderManagement_shippingWarning__7FIoS {
  background: #fef3c7;
  color: #92400e;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #fbbf24;
}

/* Order Details */
.EnhancedOrderManagement_orderDetails__WHnTr {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
  margin-top: 1rem;
  max-width: 100%;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.EnhancedOrderManagement_detailsSection__9rpi4 h5 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1rem;
}

.EnhancedOrderManagement_detailsGrid__xkhy6 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.EnhancedOrderManagement_detailItem__4RgHY {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.EnhancedOrderManagement_detailLabel__fzzr0 {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  font-weight: 600;
}

.EnhancedOrderManagement_detailValue__H1Ont {
  font-size: 0.875rem;
  color: #2d3748;
  font-weight: 500;
}

/* Items Section */
.EnhancedOrderManagement_itemsSection__MvBv6 {
  margin-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.EnhancedOrderManagement_itemsHeader__hxf3Q {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.EnhancedOrderManagement_itemsHeader__hxf3Q h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1rem;
}

.EnhancedOrderManagement_itemActions__TrZ9O {
  display: flex;
  gap: 0.75rem;
}

.EnhancedOrderManagement_addProductBtn__7_Alk, .EnhancedOrderManagement_addTemplateBtn___DQk_ {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.EnhancedOrderManagement_addProductBtn__7_Alk:hover, .EnhancedOrderManagement_addTemplateBtn___DQk_:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.EnhancedOrderManagement_addTemplateBtn___DQk_ {
  background: #38b2ac;
}

.EnhancedOrderManagement_addTemplateBtn___DQk_:hover {
  background: #319795;
}

/* Items List */
.EnhancedOrderManagement_itemsList__mRYUs {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  max-width: 100%;
  box-sizing: border-box;
}

.EnhancedOrderManagement_emptyItems__2Dutu {
  text-align: center;
  color: #718096;
  padding: 2rem;
}

.EnhancedOrderManagement_emptyItems__2Dutu p {
  margin: 0.5rem 0;
}

.EnhancedOrderManagement_orderItem__a7BCa {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
  min-width: 0;
}

.EnhancedOrderManagement_orderItem__a7BCa:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.EnhancedOrderManagement_orderItem__a7BCa:last-child {
  margin-bottom: 0;
}

.EnhancedOrderManagement_itemInfo__Tl9jH {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.EnhancedOrderManagement_itemName__6wpyG {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.4;
}

.EnhancedOrderManagement_itemDescription__e5z_g {
  color: #718096;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.EnhancedOrderManagement_itemMeta__LE3Pg {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.EnhancedOrderManagement_itemCategory__npdUl, .EnhancedOrderManagement_itemQuantity__eSMl_, .EnhancedOrderManagement_itemValue__I9cTC {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.EnhancedOrderManagement_itemValue__I9cTC {
  background: #c6f6d5;
  color: #22543d;
}

.EnhancedOrderManagement_removeItemBtn__oNglh {
  background: #fed7d7;
  color: #c53030;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.EnhancedOrderManagement_removeItemBtn__oNglh:hover {
  background: #feb2b2;
  transform: scale(1.1);
}

.EnhancedOrderManagement_loading__iOtTU {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

.EnhancedOrderManagement_emptyState__FFR4c {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

.EnhancedOrderManagement_emptyStateContent__hTGUi h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.EnhancedOrderManagement_emptyStateContent__hTGUi p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

.EnhancedOrderManagement_emptyStateHint__DFhKa {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #d4af37;
}

.EnhancedOrderManagement_emptyStateHint__DFhKa p {
  margin: 0;
  font-size: 0.875rem;
  color: #4a5568;
}

.EnhancedOrderManagement_ordersGrid__WwaL4 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 1.5rem;
}

/* Enhanced Order Card */
.EnhancedOrderManagement_orderCard__AtYst {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.EnhancedOrderManagement_orderHeader__cppKj {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.EnhancedOrderManagement_orderHeader__cppKj h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.EnhancedOrderManagement_statusContainer__P1d2g {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.EnhancedOrderManagement_statusBadge__PA8_v {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.EnhancedOrderManagement_statusBtn__N7Di0 {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.EnhancedOrderManagement_statusBtn__N7Di0:hover {
  background: #3182ce;
}

.EnhancedOrderManagement_orderMeta__0K8kG {
  padding: 0 1.5rem 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.EnhancedOrderManagement_orderMetaRow__q7FP0 {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.EnhancedOrderManagement_metaItem__tNeaM {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
  flex: 1;
}

.EnhancedOrderManagement_metaLabel__00b7L {
  font-weight: 500;
  color: #718096;
  min-width: 80px;
}

.EnhancedOrderManagement_metaValue__H6_of {
  font-weight: 600;
  color: #2d3748;
  word-wrap: break-word;
  overflow-wrap: break-word;
  min-width: 0;
}

.EnhancedOrderManagement_metaItem__tNeaM:first-child .EnhancedOrderManagement_metaValue__H6_of {
  color: #d4af37;
}

.EnhancedOrderManagement_metaItem__tNeaM:nth-child(2) .EnhancedOrderManagement_metaValue__H6_of {
  color: #4299e1;
}

.EnhancedOrderManagement_orderItems__4sy4Z {
  padding: 1.5rem;
}

.EnhancedOrderManagement_itemsHeader__hxf3Q {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.EnhancedOrderManagement_itemsHeader__hxf3Q h5 {
  margin: 0;
  color: #4a5568;
  font-size: 1rem;
}

.EnhancedOrderManagement_itemActions__TrZ9O {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.EnhancedOrderManagement_actionGroup__vIDn3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.EnhancedOrderManagement_actionLabel__UkqOq {
  font-size: 0.75rem;
  color: #718096;
  font-weight: 500;
}

.EnhancedOrderManagement_templateBtn__ES99i {
  padding: 0.5rem 1rem;
  background: #9f7aea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.EnhancedOrderManagement_templateBtn__ES99i:hover {
  background: #805ad5;
}

.EnhancedOrderManagement_addItemBtn__YgZNz {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.EnhancedOrderManagement_addItemBtn__YgZNz:hover {
  background: #3182ce;
}

.EnhancedOrderManagement_orderItem__a7BCa {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  position: relative;
}

.EnhancedOrderManagement_itemImage__y8CnE {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.EnhancedOrderManagement_itemImage__y8CnE img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.EnhancedOrderManagement_noImage__q0iJI {
  color: #a0aec0;
  font-size: 0.75rem;
  text-align: center;
}

.EnhancedOrderManagement_itemInfo__Tl9jH {
  flex: 1;
}

.EnhancedOrderManagement_itemInfo__Tl9jH strong {
  color: #2d3748;
  display: block;
  margin-bottom: 0.25rem;
}

.EnhancedOrderManagement_itemInfo__Tl9jH p {
  margin: 0 0 0.5rem 0;
  color: #718096;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_itemMeta__LE3Pg {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.EnhancedOrderManagement_categoryTag__PTM3e {
  background: #d4af37;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.EnhancedOrderManagement_quantity__aeBmk {
  background: #4299e1;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.EnhancedOrderManagement_value__TIIOV {
  background: #48bb78;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.EnhancedOrderManagement_removeBtn__Z0CwP {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.EnhancedOrderManagement_removeBtn__Z0CwP:hover {
  background: #e53e3e;
}

.EnhancedOrderManagement_orderActions__cem5H {
  display: flex;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  justify-content: flex-end;
}

.EnhancedOrderManagement_notifyBtn__XTlVr {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background 0.2s;
}

.EnhancedOrderManagement_notifyBtn__XTlVr:hover {
  background: #b8941f;
}

.EnhancedOrderManagement_notifiedBadge__Uz9OC {
  padding: 0.75rem 1.5rem;
  background: #c6f6d5;
  color: #22543d;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Modal Styles */
.EnhancedOrderManagement_modalOverlay__BPLCS {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.EnhancedOrderManagement_modal__v2_z8 {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
  width: 900px;
  max-width: 95vw;
  height: 80vh;
  max-height: 700px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.EnhancedOrderManagement_modalHeader__72dAI {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.EnhancedOrderManagement_modalHeader__72dAI h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.EnhancedOrderManagement_closeBtn__LvHnx {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.EnhancedOrderManagement_closeBtn__LvHnx:hover {
  background: #f7fafc;
}

.EnhancedOrderManagement_modalContent__Nwhbk {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.EnhancedOrderManagement_formGroup__Ap2kC {
  margin-bottom: 1.5rem;
}

.EnhancedOrderManagement_formGroup__Ap2kC label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_formGroup__Ap2kC input,
.EnhancedOrderManagement_formGroup__Ap2kC textarea,
.EnhancedOrderManagement_formGroup__Ap2kC select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.EnhancedOrderManagement_formGroup__Ap2kC input:focus,
.EnhancedOrderManagement_formGroup__Ap2kC textarea:focus,
.EnhancedOrderManagement_formGroup__Ap2kC select:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.EnhancedOrderManagement_statusSelect__r1iYC {
  background: white;
}

.EnhancedOrderManagement_statusInfo__FvVKg {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.EnhancedOrderManagement_statusInfo__FvVKg h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_statusInfo__FvVKg ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #4a5568;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_modalActions__iZUWR {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.EnhancedOrderManagement_cancelBtn__MdiFF {
  padding: 0.75rem 1.5rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.EnhancedOrderManagement_cancelBtn__MdiFF:hover {
  background: #cbd5e0;
}

.EnhancedOrderManagement_updateBtn__xIZZP,
.EnhancedOrderManagement_addBtn__u6Yd_,
.EnhancedOrderManagement_applyBtn__TyDqf {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.EnhancedOrderManagement_updateBtn__xIZZP:hover:not(:disabled),
.EnhancedOrderManagement_addBtn__u6Yd_:hover:not(:disabled),
.EnhancedOrderManagement_applyBtn__TyDqf:hover:not(:disabled) {
  background: #b8941f;
}

.EnhancedOrderManagement_updateBtn__xIZZP:disabled,
.EnhancedOrderManagement_addBtn__u6Yd_:disabled,
.EnhancedOrderManagement_applyBtn__TyDqf:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Product Search Modal */
.EnhancedOrderManagement_searchSection__ernmb {
  margin-bottom: 1.5rem;
}

.EnhancedOrderManagement_searchFilters__rf_9I {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.EnhancedOrderManagement_searchInput__bMgM6 {
  flex: 2;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_categoryFilter__Onyv9 {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
}

.EnhancedOrderManagement_searchHint__bQWzE {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0;
  font-style: italic;
}

.EnhancedOrderManagement_productsList__YMcy0 {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.EnhancedOrderManagement_noProducts__d5KT6 {
  padding: 2rem;
  text-align: center;
  color: #64748b;
  font-style: italic;
}

.EnhancedOrderManagement_productItem__0nPE3 {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: background 0.2s;
}

.EnhancedOrderManagement_productItem__0nPE3:hover {
  background: #f7fafc;
}

.EnhancedOrderManagement_productItem__0nPE3.EnhancedOrderManagement_selected__8BlM8 {
  background: #e6fffa;
  border-color: #38b2ac;
}

.EnhancedOrderManagement_productItem__0nPE3:last-child {
  border-bottom: none;
}

.EnhancedOrderManagement_productImage__LXa60 {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.EnhancedOrderManagement_productImage__LXa60 img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.EnhancedOrderManagement_productInfo__cM2Y2 h4 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 1rem;
}

.EnhancedOrderManagement_productInfo__cM2Y2 p {
  margin: 0;
  color: #718096;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_price__JqzRB {
  color: #d4af37;
  font-weight: 600;
}

.EnhancedOrderManagement_stock__S0fx_ {
  color: #4a5568;
}

.EnhancedOrderManagement_selectionSection__652_6 {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
}

.EnhancedOrderManagement_selectionSection__652_6 h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
}

.EnhancedOrderManagement_quantitySection__fP3qn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.EnhancedOrderManagement_quantitySection__fP3qn label {
  margin: 0;
  color: #4a5568;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_quantitySection__fP3qn input {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

/* Template Selection Modal */
.EnhancedOrderManagement_templatesList__HULn1 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.EnhancedOrderManagement_templateCard__dWpSJ {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
  background: white;
}

.EnhancedOrderManagement_templateCard__dWpSJ:hover {
  border-color: #d4af37;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.1);
}

.EnhancedOrderManagement_templateInfo__Jfml9 h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
}

.EnhancedOrderManagement_templateInfo__Jfml9 p {
  margin: 0 0 1rem 0;
  color: #718096;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_templateMeta__dGLFC {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.EnhancedOrderManagement_templateType__mwmRk,
.EnhancedOrderManagement_season__AY_ap,
.EnhancedOrderManagement_itemCount__5rKPk,
.EnhancedOrderManagement_totalValue__vOjwQ {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.EnhancedOrderManagement_templateType__mwmRk {
  background: #e6fffa;
  color: #234e52;
}

.EnhancedOrderManagement_season__AY_ap {
  background: #fef5e7;
  color: #744210;
}

.EnhancedOrderManagement_itemCount__5rKPk {
  background: #e6f3ff;
  color: #1a365d;
}

.EnhancedOrderManagement_totalValue__vOjwQ {
  background: #f0fff4;
  color: #22543d;
}

.EnhancedOrderManagement_templatePreview__D2pPE {
  margin: 1rem 0;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
}

.EnhancedOrderManagement_templatePreview__D2pPE h5 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_templateItems__U_5Ly {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.EnhancedOrderManagement_templateItem__TUiEz {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.EnhancedOrderManagement_itemImage__y8CnE {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.EnhancedOrderManagement_itemImage__y8CnE img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.EnhancedOrderManagement_itemDetails__WSPKD {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.EnhancedOrderManagement_itemName__6wpyG {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_itemBrand__sKWiS {
  color: #718096;
  font-size: 0.75rem;
}

.EnhancedOrderManagement_itemPrice__di5eO {
  color: #d4af37;
  font-weight: 600;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_itemQuantity__eSMl_ {
  color: #4a5568;
  font-size: 0.75rem;
  background: #edf2f7;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  align-self: flex-start;
}

.EnhancedOrderManagement_templateActions___MmbQ {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.EnhancedOrderManagement_templateSummary__Tt0K4 {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.EnhancedOrderManagement_templateSummary__Tt0K4 span:first-child {
  color: #4a5568;
}

.EnhancedOrderManagement_templateSummary__Tt0K4 span:last-child {
  color: #d4af37;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .EnhancedOrderManagement_ordersGrid__WwaL4 {
    grid-template-columns: 1fr;
  }
  
  .EnhancedOrderManagement_orderHeader__cppKj {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .EnhancedOrderManagement_statusContainer__P1d2g {
    width: 100%;
    justify-content: space-between;
  }
  
  .EnhancedOrderManagement_itemActions__TrZ9O {
    flex-direction: column;
    width: 100%;
  }
  
  .EnhancedOrderManagement_orderActions__cem5H {
    flex-direction: column;
  }
  
  .EnhancedOrderManagement_modal__v2_z8 {
    width: 95vw !important;
    height: 90vh !important;
    max-height: 90vh !important;
    margin: 1rem;
  }

  .EnhancedOrderManagement_modalActions__iZUWR {
    flex-direction: column;
  }

  .EnhancedOrderManagement_itemsList__mRYUs {
    display: flex !important;
    flex-direction: column !important;
  }
}

/* Stable Desktop Layout */
@media (min-width: 769px) {
  .EnhancedOrderManagement_modal__v2_z8 {
    width: 900px !important;
    max-width: 95vw !important;
    height: 80vh !important;
    max-height: 700px !important;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/TemplateManagement.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Template Management Styles */
.TemplateManagement_templateManagement__HZn7R {
  padding: 0;
}

.TemplateManagement_loadingContainer__eEgEv {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.TemplateManagement_loadingSpinner__fR4hx {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: TemplateManagement_spin__XFMhj 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes TemplateManagement_spin__XFMhj {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.TemplateManagement_header__j5fv3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.TemplateManagement_header__j5fv3 h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.TemplateManagement_createBtn__lRifm {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.TemplateManagement_createBtn__lRifm:hover {
  background: #b8941f;
}

.TemplateManagement_filters__W6OhD {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.TemplateManagement_filterSelect__jWFeC {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  flex: 1;
  min-width: 200px;
}

.TemplateManagement_filterSelect__jWFeC:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.TemplateManagement_templatesGrid__qm0gY {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.TemplateManagement_templateCard__ZTrBz {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s;
}

.TemplateManagement_templateCard__ZTrBz:hover {
  transform: translateY(-2px);
}

.TemplateManagement_templateCard__ZTrBz.TemplateManagement_inactive__ZKItw {
  opacity: 0.6;
  border: 2px dashed #e2e8f0;
}

.TemplateManagement_templateHeader__b3dzp {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.TemplateManagement_templateHeader__b3dzp h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.TemplateManagement_templateMeta__XzFfZ {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.TemplateManagement_templateType__26lb4,
.TemplateManagement_packageTier__1aipf,
.TemplateManagement_season__DpzQe {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.TemplateManagement_templateType__26lb4 {
  background: #e6fffa;
  color: #234e52;
}

.TemplateManagement_packageTier__1aipf {
  background: #fef5e7;
  color: #744210;
}

.TemplateManagement_season__DpzQe {
  background: #e6f3ff;
  color: #1a365d;
}

.TemplateManagement_templateDescription__Uu3jG {
  padding: 1rem 1.5rem;
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.5;
}

.TemplateManagement_templateItems__BKcl2 {
  padding: 1rem 1.5rem;
}

.TemplateManagement_templateItems__BKcl2 h5 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1rem;
}

.TemplateManagement_itemsPreview__4PEFb {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.TemplateManagement_itemPreview__Ikky4 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f7fafc;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  color: #4a5568;
  max-width: 150px;
}

.TemplateManagement_itemPreview__Ikky4 img {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  -o-object-fit: cover;
     object-fit: cover;
  flex-shrink: 0;
}

.TemplateManagement_itemPreview__Ikky4 span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.TemplateManagement_moreItems__9Is0v {
  background: #e2e8f0;
  color: #4a5568;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
}

.TemplateManagement_templateActions__yDkfB {
  display: flex;
  gap: 0.5rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.TemplateManagement_editBtn__gPxW7 {
  flex: 1;
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.TemplateManagement_editBtn__gPxW7:hover {
  background: #3182ce;
}

.TemplateManagement_duplicateBtn___BLqP {
  flex: 1;
  padding: 0.5rem 1rem;
  background: #9f7aea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.TemplateManagement_duplicateBtn___BLqP:hover {
  background: #805ad5;
}

.TemplateManagement_toggleBtn__cDyB0 {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.TemplateManagement_toggleBtn__cDyB0.TemplateManagement_active__QfMSi {
  background: #f56565;
  color: white;
}

.TemplateManagement_toggleBtn__cDyB0.TemplateManagement_active__QfMSi:hover {
  background: #e53e3e;
}

.TemplateManagement_toggleBtn__cDyB0.TemplateManagement_inactive__ZKItw {
  background: #48bb78;
  color: white;
}

.TemplateManagement_toggleBtn__cDyB0.TemplateManagement_inactive__ZKItw:hover {
  background: #38a169;
}

/* Modal Styles */
.TemplateManagement_modalOverlay__Ja_jS {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.TemplateManagement_modal__p1rNi {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.TemplateManagement_modalHeader__IY_be {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.TemplateManagement_modalHeader__IY_be h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.TemplateManagement_closeBtn__3qWne {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.TemplateManagement_closeBtn__3qWne:hover {
  background: #f7fafc;
}

.TemplateManagement_modalForm__9YvqW {
  padding: 1.5rem;
}

.TemplateManagement_formRow__b7kSc {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.TemplateManagement_formGroup__Bnk0k {
  margin-bottom: 1.5rem;
}

.TemplateManagement_formGroup__Bnk0k label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.TemplateManagement_formGroup__Bnk0k input,
.TemplateManagement_formGroup__Bnk0k textarea,
.TemplateManagement_formGroup__Bnk0k select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.TemplateManagement_formGroup__Bnk0k input:focus,
.TemplateManagement_formGroup__Bnk0k textarea:focus,
.TemplateManagement_formGroup__Bnk0k select:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.TemplateManagement_modalActions__kALDE {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.TemplateManagement_cancelBtn__JnV9k {
  padding: 0.75rem 1.5rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.TemplateManagement_cancelBtn__JnV9k:hover {
  background: #cbd5e0;
}

.TemplateManagement_saveBtn__tp22a {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.TemplateManagement_saveBtn__tp22a:hover {
  background: #b8941f;
}

/* Enhanced Template Modal Styles */
.TemplateManagement_productSection__xds4Z {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.TemplateManagement_sectionHeader__jNKZE {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.TemplateManagement_sectionHeader__jNKZE h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1rem;
}

.TemplateManagement_addProductBtn__CjkwP {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.TemplateManagement_addProductBtn__CjkwP:hover {
  background: #3182ce;
}

.TemplateManagement_selectedProducts__tG3jD {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.TemplateManagement_selectedProduct__P8tna {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  position: relative;
}

.TemplateManagement_selectedProduct__P8tna .TemplateManagement_productImage__Ag__K {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.TemplateManagement_selectedProduct__P8tna .TemplateManagement_productImage__Ag__K img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.TemplateManagement_selectedProduct__P8tna .TemplateManagement_productInfo__em_IB {
  flex: 1;
}

.TemplateManagement_selectedProduct__P8tna .TemplateManagement_productInfo__em_IB h5 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.TemplateManagement_selectedProduct__P8tna .TemplateManagement_productInfo__em_IB p {
  margin: 0;
  color: #718096;
  font-size: 0.75rem;
}

.TemplateManagement_selectedProduct__P8tna .TemplateManagement_price__uJuJf {
  color: #d4af37;
  font-weight: 600;
}

.TemplateManagement_quantityControl__2VB2q {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.TemplateManagement_quantityControl__2VB2q label {
  margin: 0;
  color: #4a5568;
  font-size: 0.75rem;
}

.TemplateManagement_quantityControl__2VB2q input {
  width: 60px;
  padding: 0.25rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.75rem;
}

.TemplateManagement_removeProductBtn__d_fIl {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background 0.2s;
}

.TemplateManagement_removeProductBtn__d_fIl:hover {
  background: #e53e3e;
}

.TemplateManagement_productSelector__oZ5Ij {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  background: #f7fafc;
}

.TemplateManagement_productFilters__Yd7U1 {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.TemplateManagement_searchInput__Yba0t {
  flex: 2;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.TemplateManagement_filterSelect__jWFeC {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.TemplateManagement_availableProducts__oLbcq {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.TemplateManagement_availableProduct__9P14y {
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem;
  transition: border-color 0.2s;
}

.TemplateManagement_availableProduct__9P14y:hover {
  border-color: #d4af37;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_productImage__Ag__K {
  width: 100%;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_productImage__Ag__K img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_productInfo__em_IB h5 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 0.875rem;
  line-height: 1.2;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_productInfo__em_IB p {
  margin: 0 0 0.25rem 0;
  color: #718096;
  font-size: 0.75rem;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_price__uJuJf {
  color: #d4af37;
  font-weight: 600;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_stock__l9w_k {
  color: #4a5568;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_addBtn__KPSDR {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background 0.2s;
}

.TemplateManagement_availableProduct__9P14y .TemplateManagement_addBtn__KPSDR:hover {
  background: #38a169;
}

.TemplateManagement_noProducts__mFYXX {
  grid-column: 1 / -1;
  text-align: center;
  color: #718096;
  padding: 2rem;
  font-style: italic;
}

.TemplateManagement_noImage__cZ7Am {
  color: #a0aec0;
  font-size: 0.75rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .TemplateManagement_filters__W6OhD {
    flex-direction: column;
  }
  
  .TemplateManagement_filterSelect__jWFeC {
    min-width: auto;
  }
  
  .TemplateManagement_templatesGrid__qm0gY {
    grid-template-columns: 1fr;
  }
  
  .TemplateManagement_templateActions__yDkfB {
    flex-direction: column;
  }
  
  .TemplateManagement_formRow__b7kSc {
    grid-template-columns: 1fr;
  }
  
  .TemplateManagement_modal__p1rNi {
    width: 95%;
    margin: 1rem;
  }
  
  .TemplateManagement_modalActions__kALDE {
    flex-direction: column;
  }
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/StatusConfirmationModal.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
.StatusConfirmationModal_modalOverlay__ietHg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

.StatusConfirmationModal_modal__90d3u {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: StatusConfirmationModal_modalSlideIn__5tS9s 0.3s ease-out;
}

@keyframes StatusConfirmationModal_modalSlideIn__5tS9s {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.StatusConfirmationModal_modalHeader__5SrPy {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.StatusConfirmationModal_modalHeader__5SrPy h3 {
  margin: 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
}

.StatusConfirmationModal_closeButton__JI_hY {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  color: #64748b;
  transition: all 0.2s ease;
}

.StatusConfirmationModal_closeButton__JI_hY:hover {
  background: #f1f5f9;
  color: #334155;
}

.StatusConfirmationModal_modalBody__Lz46V {
  padding: 2rem;
}

.StatusConfirmationModal_subscriptionDetails__VFdOX {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.StatusConfirmationModal_subscriptionDetails__VFdOX h4 {
  margin: 0 0 1rem 0;
  color: #334155;
  font-size: 1.125rem;
  font-weight: 600;
}

.StatusConfirmationModal_detailRow__LvLJv {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.StatusConfirmationModal_detailRow__LvLJv:last-child {
  margin-bottom: 0;
}

.StatusConfirmationModal_label__8aJG1 {
  color: #64748b;
  font-weight: 500;
}

.StatusConfirmationModal_value__Akz_y {
  color: #1e293b;
  font-weight: 600;
}

.StatusConfirmationModal_statusBadge__nZVnv {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.StatusConfirmationModal_statusChange__Dvkjd {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.StatusConfirmationModal_statusChangeHeader__V3da_ {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #0369a1;
  font-weight: 600;
}

.StatusConfirmationModal_newStatusBadge__2ghNg {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
}

.StatusConfirmationModal_warningBox__Ih2BF {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.StatusConfirmationModal_warningHeader__HsOwq {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #92400e;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.StatusConfirmationModal_warningBox__Ih2BF p {
  margin: 0;
  color: #92400e;
  line-height: 1.5;
}

.StatusConfirmationModal_confirmationText__5kWQ1 {
  text-align: center;
  color: #475569;
  line-height: 1.6;
}

.StatusConfirmationModal_confirmationText__5kWQ1 p {
  margin: 0;
}

.StatusConfirmationModal_modalFooter__Iuypn {
  display: flex;
  gap: 1rem;
  padding: 1rem 2rem 2rem 2rem;
  border-top: 1px solid #e2e8f0;
}

.StatusConfirmationModal_cancelButton__wH7a6 {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.StatusConfirmationModal_cancelButton__wH7a6:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.StatusConfirmationModal_cancelButton__wH7a6:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.StatusConfirmationModal_confirmButton__t7uWE {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.StatusConfirmationModal_confirmButton__t7uWE:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.StatusConfirmationModal_confirmButton__t7uWE:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.StatusConfirmationModal_spinner__XNF_i {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.StatusConfirmationModal_spinnerIcon__VrBJw {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: StatusConfirmationModal_spin__6C8LL 1s linear infinite;
}

@keyframes StatusConfirmationModal_spin__6C8LL {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .StatusConfirmationModal_modal__90d3u {
    width: 95%;
    margin: 1rem;
  }

  .StatusConfirmationModal_modalHeader__5SrPy {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .StatusConfirmationModal_modalHeader__5SrPy h3 {
    font-size: 1.25rem;
  }

  .StatusConfirmationModal_modalBody__Lz46V {
    padding: 1.5rem;
  }

  .StatusConfirmationModal_modalFooter__Iuypn {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    flex-direction: column;
  }

  .StatusConfirmationModal_statusChange__Dvkjd {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/components/AdminDashboard/LeModeCoTab/LeModeCoTab.module.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Le-Mode-Co Tab Styles */
.LeModeCoTab_leModeCoContainer__G8FyG {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

.LeModeCoTab_loadingContainer__bLete {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6c757d;
}

.LeModeCoTab_loadingSpinner__8_dJn {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #d4af37;
  border-radius: 50%;
  animation: LeModeCoTab_spin__Q4VNe 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes LeModeCoTab_spin__Q4VNe {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tab Header */
.LeModeCoTab_tabHeader__SONg7 {
  background: white;
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.LeModeCoTab_tabHeader__SONg7 h2 {
  margin: 0 0 1.5rem 0;
  color: #2d3748;
  font-size: 1.75rem;
  font-weight: 600;
}

.LeModeCoTab_subTabs__w0QFv {
  display: flex;
  gap: 0.5rem;
}

.LeModeCoTab_subTab__n4KT2 {
  padding: 0.75rem 1.5rem;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.LeModeCoTab_subTab__n4KT2:hover {
  background: #f7fafc;
  border-color: #d4af37;
}

.LeModeCoTab_subTab__n4KT2.LeModeCoTab_active__xAxlB {
  background: #d4af37;
  color: white;
  border-color: #d4af37;
}

/* Tab Content */
.LeModeCoTab_tabContent__Xkiad {
  padding: 2rem;
}

/* Overview Section */
.LeModeCoTab_overviewSection__R6q_3 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.LeModeCoTab_statsGrid__NuejH {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.LeModeCoTab_statCard__2GWZ_ {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.LeModeCoTab_statCard__2GWZ_::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.LeModeCoTab_statCard__2GWZ_:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.LeModeCoTab_statIcon__KWqdc {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 0.5rem;
  color: white;
}

.LeModeCoTab_statIcon__KWqdc svg {
  width: 24px;
  height: 24px;
}

.LeModeCoTab_statContent__Xmuhr h3 {
  margin: 0 0 0.5rem 0;
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.LeModeCoTab_statNumber__y_sv6 {
  margin: 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
}

.LeModeCoTab_quickActions__mnp3U {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.LeModeCoTab_quickActions__mnp3U h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.LeModeCoTab_actionBtn__xQ9QD {
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.LeModeCoTab_actionBtn__xQ9QD:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* Packages Section */
.LeModeCoTab_packagesSection__Ld5R_ {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.LeModeCoTab_sectionHeader__hQYXP {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid #f1f5f9;
  margin-bottom: 1.5rem;
}

.LeModeCoTab_sectionHeader__hQYXP h3 {
  margin: 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.LeModeCoTab_createBtn__Y6HSs {
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.LeModeCoTab_createBtn__Y6HSs:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.LeModeCoTab_packagesGrid__Wzqdd {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.LeModeCoTab_packageCard__HjHpd {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.LeModeCoTab_packageCard__HjHpd:hover {
  transform: translateY(-2px);
}

.LeModeCoTab_packageCard__HjHpd.LeModeCoTab_inactive__oOONG {
  opacity: 0.6;
  border: 2px dashed #e2e8f0;
}

.LeModeCoTab_packageHeader__yKafX {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.LeModeCoTab_packageHeader__yKafX h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.LeModeCoTab_popularBadge__PMmD8 {
  background: #d4af37;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.LeModeCoTab_packagePrice__VmCcj {
  font-size: 1.5rem;
  font-weight: 700;
  color: #d4af37;
  margin-bottom: 1rem;
}

.LeModeCoTab_packageFeatures__u8PQs {
  margin-bottom: 1.5rem;
}

.LeModeCoTab_feature__nnSU1 {
  color: #4a5568;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.LeModeCoTab_moreFeatures__iaaxZ {
  color: #718096;
  font-size: 0.75rem;
  font-style: italic;
}

.LeModeCoTab_packageActions__U8c8S {
  display: flex;
  gap: 0.75rem;
}

.LeModeCoTab_editBtn__oBoV0 {
  flex: 1;
  padding: 0.625rem 1.25rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.LeModeCoTab_editBtn__oBoV0:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.LeModeCoTab_toggleBtn__RY1h4 {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_toggleBtn__RY1h4.LeModeCoTab_active__xAxlB {
  background: #f56565;
  color: white;
}

.LeModeCoTab_toggleBtn__RY1h4.LeModeCoTab_active__xAxlB:hover {
  background: #e53e3e;
}

.LeModeCoTab_toggleBtn__RY1h4.LeModeCoTab_inactive__oOONG {
  background: #48bb78;
  color: white;
}

.LeModeCoTab_toggleBtn__RY1h4.LeModeCoTab_inactive__oOONG:hover {
  background: #38a169;
}

/* Subscriptions Section */
.LeModeCoTab_subscriptionsSection__iSnp3 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.LeModeCoTab_subscriptionsTable__n3RIO {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.LeModeCoTab_tableHeader__L_VIJ {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: #f7fafc;
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.LeModeCoTab_tableRow__M4Tya {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  align-items: center;
}

.LeModeCoTab_tableRow__M4Tya:last-child {
  border-bottom: none;
}

.LeModeCoTab_customerInfo__RlhN6 {
  display: flex;
  flex-direction: column;
}

.LeModeCoTab_customerName__lQ_06 {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.LeModeCoTab_customerEmail___6ak5 {
  font-size: 0.875rem;
  color: #718096;
}

.LeModeCoTab_statusBadge__Dy3Kf {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
}

.LeModeCoTab_rowActions__wfekS {
  display: flex;
  gap: 0.5rem;
}

.LeModeCoTab_viewBtn__UcbGY {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_viewBtn__UcbGY:hover {
  background: #3182ce;
}

.LeModeCoTab_statusCell__i3KU0 {
  display: flex;
  align-items: center;
}

.LeModeCoTab_statusSelect__4eL9u {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  cursor: pointer;
  transition: opacity 0.2s;
}

.LeModeCoTab_statusSelect__4eL9u:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.LeModeCoTab_statusSelect__4eL9u option {
  background: white;
  color: black;
}

/* Orders Section */
.LeModeCoTab_ordersSection__bjHv7 {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.LeModeCoTab_customerDetails__5AIEQ {
  display: flex;
  gap: 1rem;
  color: #718096;
  font-size: 0.875rem;
}

.LeModeCoTab_customerDetails__5AIEQ span {
  background: #f7fafc;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
}

.LeModeCoTab_ordersGrid__arXqc {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.LeModeCoTab_orderCard__rUxPu {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.LeModeCoTab_orderHeader__3KITm {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.LeModeCoTab_orderHeader__3KITm h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
}

.LeModeCoTab_orderItems__Zo6iv {
  margin-bottom: 1.5rem;
}

.LeModeCoTab_orderItems__Zo6iv h5 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1rem;
}

.LeModeCoTab_orderItem__5Z5EM {
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  margin-bottom: 0.75rem;
}

.LeModeCoTab_itemInfo__pszEH strong {
  color: #2d3748;
  display: block;
  margin-bottom: 0.25rem;
}

.LeModeCoTab_itemInfo__pszEH p {
  margin: 0 0 0.5rem 0;
  color: #718096;
  font-size: 0.875rem;
}

.LeModeCoTab_categoryTag__BpMQc {
  background: #d4af37;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.LeModeCoTab_addItemForm__QKidn {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.LeModeCoTab_addItemForm__QKidn input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.LeModeCoTab_formActions___eaVi {
  display: flex;
  gap: 0.5rem;
}

.LeModeCoTab_saveBtn__s5V06 {
  padding: 0.5rem 1rem;
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_saveBtn__s5V06:hover {
  background: #38a169;
}

.LeModeCoTab_cancelBtn__ZynCZ {
  padding: 0.5rem 1rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_cancelBtn__ZynCZ:hover {
  background: #cbd5e0;
}

.LeModeCoTab_orderActions__BimGL {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.LeModeCoTab_addItemBtn__dBiZG {
  padding: 0.5rem 1rem;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_addItemBtn__dBiZG:hover {
  background: #3182ce;
}

.LeModeCoTab_completeBtn__0KBiZ {
  padding: 0.5rem 1rem;
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_completeBtn__0KBiZ:hover {
  background: #38a169;
}

.LeModeCoTab_notifyBtn__c2Zkw {
  padding: 0.5rem 1rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_notifyBtn__c2Zkw:hover {
  background: #b8941f;
}

.LeModeCoTab_notifiedBadge__YGwnJ {
  padding: 0.5rem 1rem;
  background: #c6f6d5;
  color: #22543d;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Shipping Warning Styles */
.LeModeCoTab_shippingWarning__17l1Q {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.LeModeCoTab_warningIcon__JJGmT {
  color: #92400e;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.LeModeCoTab_warningText__v4JjV {
  flex: 1;
}

.LeModeCoTab_warningText__v4JjV strong {
  color: #92400e;
  font-weight: 600;
  display: block;
  margin-bottom: 0.25rem;
}

.LeModeCoTab_warningText__v4JjV p {
  margin: 0;
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.4;
}

.LeModeCoTab_disabledBtn__Z_vf6 {
  opacity: 0.5;
  cursor: not-allowed !important;
  background: #9ca3af !important;
}

.LeModeCoTab_disabledBtn__Z_vf6:hover {
  transform: none !important;
  box-shadow: none !important;
}

.LeModeCoTab_emptyState__pXzd2 {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

.LeModeCoTab_loading__eFIQi {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  color: #718096;
}

/* Modal Styles */
.LeModeCoTab_modalOverlay__H0GzU {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.LeModeCoTab_modal__Q1Xiy {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.LeModeCoTab_modalHeader__VmL2X {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.LeModeCoTab_modalHeader__VmL2X h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.LeModeCoTab_closeBtn__qVjOR {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.LeModeCoTab_closeBtn__qVjOR:hover {
  background: #f7fafc;
}

.LeModeCoTab_modalForm__utKXi {
  padding: 1.5rem;
}

.LeModeCoTab_formGroup__JEqgK {
  margin-bottom: 1.5rem;
}

.LeModeCoTab_formGroup__JEqgK label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.875rem;
}

.LeModeCoTab_formGroup__JEqgK input,
.LeModeCoTab_formGroup__JEqgK textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.LeModeCoTab_formGroup__JEqgK input:focus,
.LeModeCoTab_formGroup__JEqgK textarea:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.LeModeCoTab_featureInput__iuDqL {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.LeModeCoTab_featureInput__iuDqL input {
  flex: 1;
  margin-bottom: 0;
}

.LeModeCoTab_removeFeatureBtn__LHUqD {
  background: #f56565;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}

.LeModeCoTab_removeFeatureBtn__LHUqD:hover {
  background: #e53e3e;
}

.LeModeCoTab_addFeatureBtn__vt0J8 {
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_addFeatureBtn__vt0J8:hover {
  background: #3182ce;
}

.LeModeCoTab_checkboxLabel__xWHi7 {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.LeModeCoTab_checkboxLabel__xWHi7 input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.LeModeCoTab_modalActions__YvxL2 {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.LeModeCoTab_modalActions__YvxL2 .LeModeCoTab_cancelBtn__ZynCZ {
  background: #e2e8f0;
  color: #4a5568;
}

.LeModeCoTab_modalActions__YvxL2 .LeModeCoTab_saveBtn__s5V06 {
  background: #d4af37;
  color: white;
}

.LeModeCoTab_modalActions__YvxL2 .LeModeCoTab_saveBtn__s5V06:hover {
  background: #b8941f;
}

/* Category Management Styles */
.LeModeCoTab_categoryManagement__hqZ2F {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.LeModeCoTab_categoriesGrid__UAQPe {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.LeModeCoTab_categoryCard__cy3tz {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.LeModeCoTab_categoryCard__cy3tz:hover {
  transform: translateY(-2px);
}

.LeModeCoTab_categoryHeader__bnZjd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.LeModeCoTab_categoryHeader__bnZjd h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
}

.LeModeCoTab_categoryActions__7_SaV {
  display: flex;
  gap: 0.5rem;
}

.LeModeCoTab_categoryDescription__Yw6nV {
  color: #718096;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.LeModeCoTab_categoryMeta__a616L {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.LeModeCoTab_status__jzaEG {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.LeModeCoTab_status__jzaEG.LeModeCoTab_active__xAxlB {
  background: #c6f6d5;
  color: #22543d;
}

.LeModeCoTab_status__jzaEG.LeModeCoTab_inactive__oOONG {
  background: #fed7d7;
  color: #742a2a;
}

.LeModeCoTab_order__yWjcl {
  color: #718096;
  font-weight: 500;
}

.LeModeCoTab_deleteBtn__04dxT {
  padding: 0.5rem 1rem;
  background: #f56565;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s;
}

.LeModeCoTab_deleteBtn__04dxT:hover {
  background: #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
  .LeModeCoTab_tabContent__Xkiad {
    padding: 1rem;
  }

  .LeModeCoTab_statsGrid__NuejH {
    grid-template-columns: 1fr;
  }

  .LeModeCoTab_packagesGrid__Wzqdd {
    grid-template-columns: 1fr;
  }

  .LeModeCoTab_ordersGrid__arXqc {
    grid-template-columns: 1fr;
  }

  .LeModeCoTab_categoriesGrid__UAQPe {
    grid-template-columns: 1fr;
  }

  .LeModeCoTab_tableHeader__L_VIJ,
  .LeModeCoTab_tableRow__M4Tya {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .LeModeCoTab_tableHeader__L_VIJ {
    display: none;
  }

  .LeModeCoTab_tableRow__M4Tya {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 1rem;
  }

  .LeModeCoTab_subTabs__w0QFv {
    flex-wrap: wrap;
  }

  .LeModeCoTab_modal__Q1Xiy {
    width: 95%;
    margin: 1rem;
  }

  .LeModeCoTab_categoryActions__7_SaV {
    flex-direction: column;
  }

  .LeModeCoTab_categoryMeta__a616L {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/admin/dashboard/AdminDashboard.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/* Dashboard Container */
.AdminDashboard_dashboardContainer__m_kWl {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

/* Sidebar Styles */
.AdminDashboard_sidebar__lbspw {
  width: 280px;
  background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.AdminDashboard_sidebarHeader__eyCmY {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(192, 192, 192, 0.1);
}

.AdminDashboard_logo__fOEUO {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.AdminDashboard_logoIcon__JQueZ {
  font-size: 1.5rem;
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.AdminDashboard_logoText__FofCz {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  letter-spacing: -0.02em;
}

.AdminDashboard_adminInfo__mxJi4 {
  color: #c0c0c0;
  font-size: 0.875rem;
  margin: 0;
}

/* Sidebar Navigation */
.AdminDashboard_sidebarNav__qM0s9 {
  flex: 1;
  padding: 1rem 0;
}

.AdminDashboard_navItem__0YBFc {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #c0c0c0;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.AdminDashboard_navItem__0YBFc:hover {
  background: rgba(192, 192, 192, 0.1);
  color: white;
}

.AdminDashboard_navItem__0YBFc.AdminDashboard_active__8IfNN {
  background: linear-gradient(90deg, rgba(192, 192, 192, 0.2), rgba(169, 169, 169, 0.1));
  color: white;
  border-right: 3px solid #c0c0c0;
}

.AdminDashboard_navIcon__VYrZM {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.AdminDashboard_sidebarFooter__NF8tD {
  padding: 1rem 0;
  border-top: 1px solid rgba(192, 192, 192, 0.1);
}

.AdminDashboard_logoutButton__kHLq9 {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #ef4444;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.AdminDashboard_logoutButton__kHLq9:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Main Content */
.AdminDashboard_mainContent__xbnpL {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
}

.AdminDashboard_contentHeader__jsYAJ {
  background: white;
  padding: 2rem 2.5rem;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.AdminDashboard_pageTitle__8tj4v {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: -0.02em;
}

.AdminDashboard_contentBody__Q_nV7 {
  flex: 1;
  padding: 2.5rem;
  overflow-y: auto;
}

/* Loading States */
.AdminDashboard_loadingContainer__zJxI_ {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f8f9fa;
  gap: 1rem;
}

.AdminDashboard_loadingSpinner__OtJdg {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #c0c0c0;
  border-radius: 50%;
  animation: AdminDashboard_spin__DJhsD 1s linear infinite;
}

@keyframes AdminDashboard_spin__DJhsD {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.AdminDashboard_loading__hG7f7 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Overview Tab */
.AdminDashboard_overviewGrid__uoJSP {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.AdminDashboard_statCard__0qiPK {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.2s ease;
}

.AdminDashboard_statCard__0qiPK:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.AdminDashboard_statIcon___G9CZ {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  border-radius: 12px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminDashboard_statContent__wNMDK h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.AdminDashboard_statNumber__rUmMQ {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: -0.02em;
}

/* Tab Content */
.AdminDashboard_tabContent__56Lg3 {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.AdminDashboard_tabHeader__C_oUv {
  display: flex;
  justify-content: between;
  align-items: center;
  gap: 1rem;
}

.AdminDashboard_tabHeader__C_oUv h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.AdminDashboard_generateInvoiceBtn__DDqUe {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.AdminDashboard_generateInvoiceBtn__DDqUe:hover {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Quotes Grid */
.AdminDashboard_quotesGrid__TtX_V {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.AdminDashboard_emptyState__TJ59Q {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Quote Cards */
.AdminDashboard_quoteCard__NLFMd {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.AdminDashboard_quoteCard__NLFMd:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.AdminDashboard_quoteHeader__37fw1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.AdminDashboard_statusBadge__bx6uA {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.AdminDashboard_statusBadge__bx6uA.AdminDashboard_pending__qxt1v {
  background: rgba(251, 191, 36, 0.1);
  color: #d97706;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.AdminDashboard_statusBadge__bx6uA.AdminDashboard_quoted__N5LOw {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.AdminDashboard_quoteDate__SIUwb {
  font-size: 0.875rem;
  color: #6b7280;
}

.AdminDashboard_quoteContent___PGPg h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
}

.AdminDashboard_quoteEmail__lN_gK {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.AdminDashboard_quoteDetails__sSjtx {
  margin: 1rem 0;
}

.AdminDashboard_quoteDetails__sSjtx p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: #4b5563;
}

.AdminDashboard_quoteMessage__o28kX {
  background: #f9fafb;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #4b5563;
  margin: 1rem 0;
  border-left: 3px solid #c0c0c0;
}

.AdminDashboard_quoteActions__B2Ici {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.AdminDashboard_quoteButton__d4OCn {
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.AdminDashboard_quoteButton__d4OCn:hover {
  background: linear-gradient(135deg, #a9a9a9, #909090);
  transform: translateY(-1px);
}

.AdminDashboard_quotedInfo__9240N {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.AdminDashboard_quotedInfo__9240N p {
  margin: 0;
  font-weight: 600;
  color: #1a1a1a;
}

.AdminDashboard_resendButton__t8S9p {
  background: none;
  border: 1px solid #c0c0c0;
  color: #c0c0c0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.AdminDashboard_resendButton__t8S9p:hover {
  background: #c0c0c0;
  color: white;
}

/* Modal Styles */
.AdminDashboard_modalOverlay__D_7qf {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  overflow-y: auto;
  padding: 2rem;
}

.AdminDashboard_modal__rMrpu {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  margin: auto;
  position: relative;
}

.AdminDashboard_modalHeader__4rEA6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #f3f4f6;
}

.AdminDashboard_modalHeader__4rEA6 h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
}

.AdminDashboard_closeButton__Pq109 {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.AdminDashboard_closeButton__Pq109:hover {
  background: #f3f4f6;
  color: #1a1a1a;
}

.AdminDashboard_quoteForm__aQQmM {
  padding: 1rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.AdminDashboard_formGroup__YnC8e {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.AdminDashboard_label__EPL18 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.AdminDashboard_input__Kf1vR, .AdminDashboard_textarea__StAW5 {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  color: #1a1a1a;
  transition: all 0.2s ease;
  outline: none;
}

.AdminDashboard_input__Kf1vR:focus, .AdminDashboard_textarea__StAW5:focus {
  border-color: #c0c0c0;
  box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.1);
}

.AdminDashboard_textarea__StAW5 {
  resize: vertical;
  min-height: 80px;
}

.AdminDashboard_modalActions__jCcYa {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.AdminDashboard_cancelButton__WBUVw {
  background: none;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.AdminDashboard_cancelButton__WBUVw:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.AdminDashboard_submitButton__tKAb1 {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.AdminDashboard_submitButton__tKAb1:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
}

.AdminDashboard_submitButton__tKAb1:disabled, .AdminDashboard_cancelButton__WBUVw:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.AdminDashboard_errorMessage__j0MEU {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  margin: 0 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .AdminDashboard_sidebar__lbspw {
    width: 240px;
  }
  
  .AdminDashboard_mainContent__xbnpL {
    margin-left: 240px;
  }
  
  .AdminDashboard_quotesGrid__TtX_V {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .AdminDashboard_sidebar__lbspw {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .AdminDashboard_mainContent__xbnpL {
    margin-left: 0;
  }

  .AdminDashboard_contentHeader__jsYAJ {
    padding: 1.5rem;
  }

  .AdminDashboard_contentBody__Q_nV7 {
    padding: 1.5rem;
  }

  .AdminDashboard_overviewGrid__uoJSP {
    grid-template-columns: 1fr;
  }

  .AdminDashboard_modalOverlay__D_7qf {
    padding: 1rem;
    align-items: flex-start;
    padding-top: 2rem;
  }

  .AdminDashboard_modal__rMrpu {
    width: 95%;
    max-height: 85vh;
    margin-top: 0;
  }

  .AdminDashboard_quoteForm__aQQmM {
    max-height: calc(85vh - 100px);
  }
}

