.quotePageContainer {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem;
}

.header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.backButton {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  background: linear-gradient(135deg, #4b5563, #374151);
  transform: translateY(-1px);
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1200px;
}

.quoteDetails {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.quoteDetails h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 1.5rem 0;
  border-bottom: 2px solid #f3f4f6;
  padding-bottom: 0.75rem;
}

.detailsGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailItem label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detailItem span {
  font-size: 1rem;
  color: #1a1a1a;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.quoteForm {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  height: fit-content;
}

.quoteForm h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 1.5rem 0;
  border-bottom: 2px solid #f3f4f6;
  padding-bottom: 0.75rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.input, .textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  color: #1a1a1a;
  transition: all 0.2s ease;
  outline: none;
}

.input:focus, .textarea:focus {
  border-color: #c0c0c0;
  box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.cancelButton {
  background: none;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.submitButton {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submitButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
}

.submitButton:disabled, .cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.errorMessage {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.successMessage {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: #16a34a;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.loadingContainer, .errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f8f9fa;
  gap: 1rem;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #c0c0c0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .quotePageContainer {
    padding: 1rem;
  }
  
  .content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .quoteDetails, .quoteForm {
    padding: 1.5rem;
  }
  
  .formActions {
    flex-direction: column;
  }
}
