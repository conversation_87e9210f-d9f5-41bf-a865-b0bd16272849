/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./app/admin/AdminLogin.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
.AdminLogin_loginContainer__P88Q5 {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.AdminLogin_loginContainer__P88Q5::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(192, 192, 192, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(169, 169, 169, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.AdminLogin_loginBox__QhJil {
  background: rgba(248, 249, 250, 0.95);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(192, 192, 192, 0.2);
  position: relative;
  z-index: 1;
}

.AdminLogin_logoSection__CyE1H {
  text-align: center;
  margin-bottom: 2.5rem;
}

.AdminLogin_logo__Jb8vs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.AdminLogin_logoIcon__MsMbl {
  font-size: 2rem;
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.AdminLogin_logoText___gIcg {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.AdminLogin_subtitle__WwQoj {
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.AdminLogin_loginForm__0UsDY {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.AdminLogin_title__T2XtY {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.01em;
}

.AdminLogin_errorMessage__mSvCM {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  text-align: center;
}

.AdminLogin_inputGroup__w7rpH {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.AdminLogin_label__SXQPD {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.AdminLogin_input__eznrP {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: #ffffff;
  color: #1a1a1a;
  transition: all 0.2s ease;
  outline: none;
}

.AdminLogin_input__eznrP:focus {
  border-color: #c0c0c0;
  box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.1);
}

.AdminLogin_input__eznrP::-moz-placeholder {
  color: #9ca3af;
}

.AdminLogin_input__eznrP::placeholder {
  color: #9ca3af;
}

.AdminLogin_loginButton__OeAGo {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.AdminLogin_loginButton__OeAGo:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.AdminLogin_loginButton__OeAGo:active {
  transform: translateY(0);
}

.AdminLogin_loginButton__OeAGo:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.AdminLogin_loadingSpinner__DvV_9 {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: AdminLogin_spin__PZ6fu 1s linear infinite;
}

@keyframes AdminLogin_spin__PZ6fu {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.AdminLogin_footer__uXZ7l {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.AdminLogin_footer__uXZ7l p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 640px) {
  .AdminLogin_loginContainer__P88Q5 {
    padding: 1rem;
  }
  
  .AdminLogin_loginBox__QhJil {
    padding: 2rem;
  }
  
  .AdminLogin_logoText___gIcg {
    font-size: 1.75rem;
  }
  
  .AdminLogin_title__T2XtY {
    font-size: 1.5rem;
  }
}

