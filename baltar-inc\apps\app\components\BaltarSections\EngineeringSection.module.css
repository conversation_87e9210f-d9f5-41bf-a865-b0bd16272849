/* Archon Engineering Section - Meta Style */
.section {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
}

.videoContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.backgroundVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.4) contrast(1.2) saturate(1.1);
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(100, 100, 100, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(80, 80, 80, 0.2) 100%
  );
  z-index: 2;
}

.content {
  position: relative;
  z-index: 10;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #9ca3af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.serviceContainer {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.mainServiceCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 3rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
}

.mainServiceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(156, 163, 175, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.mainServiceCard:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(156, 163, 175, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.mainServiceCard:hover::before {
  opacity: 1;
}

.intro {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  text-align: center;
}

.featuresGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.featureColumn h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgba(156, 163, 175, 0.9);
  margin-bottom: 1rem;
  text-align: center;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureList li {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
}

.featureList li::before {
  content: '🏗️';
  position: absolute;
  left: 0;
  font-size: 0.8rem;
}

.serviceDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(156, 163, 175, 0.1);
  border-left: 3px solid rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  text-align: center;
}

.serviceLink {
  display: inline-flex;
  align-items: center;
  color: rgba(156, 163, 175, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  margin-top: 1rem;
  justify-content: center;
  width: 100%;
}

.serviceLink:hover {
  color: white;
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .section {
    padding: 4rem 0;
  }
  
  .content {
    padding: 0 1rem;
  }
  
  .header {
    margin-bottom: 2rem;
  }
  
  .mainServiceCard {
    padding: 2rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .mainServiceCard {
    padding: 1.5rem;
  }
  
  .featuresGrid {
    gap: 1rem;
  }
}
