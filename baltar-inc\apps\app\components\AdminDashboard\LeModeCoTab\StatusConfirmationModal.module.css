.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.modalHeader h3 {
  margin: 0;
  color: #1a202c;
  font-size: 1.5rem;
  font-weight: 700;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  color: #64748b;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #f1f5f9;
  color: #334155;
}

.modalBody {
  padding: 2rem;
}

.subscriptionDetails {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.subscriptionDetails h4 {
  margin: 0 0 1rem 0;
  color: #334155;
  font-size: 1.125rem;
  font-weight: 600;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.detailRow:last-child {
  margin-bottom: 0;
}

.label {
  color: #64748b;
  font-weight: 500;
}

.value {
  color: #1e293b;
  font-weight: 600;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.statusChange {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.statusChangeHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #0369a1;
  font-weight: 600;
}

.newStatusBadge {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
}

.warningBox {
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.warningHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #92400e;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.warningBox p {
  margin: 0;
  color: #92400e;
  line-height: 1.5;
}

.confirmationText {
  text-align: center;
  color: #475569;
  line-height: 1.6;
}

.confirmationText p {
  margin: 0;
}

.modalFooter {
  display: flex;
  gap: 1rem;
  padding: 1rem 2rem 2rem 2rem;
  border-top: 1px solid #e2e8f0;
}

.cancelButton {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.confirmButton {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.confirmButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.confirmButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinnerIcon {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    width: 95%;
    margin: 1rem;
  }

  .modalHeader {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .modalHeader h3 {
    font-size: 1.25rem;
  }

  .modalBody {
    padding: 1.5rem;
  }

  .modalFooter {
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    flex-direction: column;
  }

  .statusChange {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
