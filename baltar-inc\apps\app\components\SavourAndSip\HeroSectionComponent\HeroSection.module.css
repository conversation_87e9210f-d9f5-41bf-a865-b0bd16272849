.heroContainer {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Swiper and video */
.swiper, .slide {
  width: 100%;
  height: 100%;
}

.swiper {
  --swiper-pagination-color: #d4af37;
  --swiper-pagination-bullet-inactive-color: rgba(255, 255, 255, 0.5);
  --swiper-pagination-bullet-size: 12px;
  --swiper-pagination-bullet-horizontal-gap: 8px;
}

.bgVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  pointer-events: none;
  filter: brightness(0.8) contrast(1.1);
  transition: transform 8s ease-in-out;
}

.bgVideo:hover {
  transform: scale(1.05);
}

/* Overlay */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 1;
}

/* Navbar */
.heroNavbar {
  position: fixed;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.menuButton, .locationButton {
  background: rgba(212, 175, 55, 0.9);
  border: 2px solid #d4af37;
  color: white;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  border-radius: 25px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.menuButton:hover, .locationButton:hover {
  background: #d4af37;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Hero Content */
.heroContent {
  position: absolute;
  bottom: 80px;
  left: 50px;
  z-index: 5;
  color: white;
  max-width: 600px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.heroContent h1 {
  font-size: 48px;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInUp 1s ease-out;
}

.heroContent p {
  font-size: 20px;
  margin-bottom: 30px;
  opacity: 0.95;
  line-height: 1.6;
  font-weight: 400;
  animation: fadeInUp 1s ease-out 0.3s both;
}

.heroButton {
  padding: 16px 32px;
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(135deg, #d4af37 0%, #f4e4a6 100%);
  color: #1a1a1a;
  border: none;
  cursor: pointer;
  border-radius: 30px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.3);
  animation: fadeInUp 1s ease-out 0.6s both;
}

.heroButton:hover {
  background: linear-gradient(135deg, #f4e4a6 0%, #d4af37 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Sidebar Backdrop */
.menuBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.7);
  z-index: 9;
}

/* Sidebar Menu */
.sidebarMenu {
  position: fixed;
  top: 0;
  left: 0;
  width: 450px;
  max-width: 90%;
  height: 100%;
  background: #faf7f2;
  z-index: 10;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  animation: slideIn 0.4s ease forwards;
  box-shadow: 5px 0px 20px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.closeButton {
  position: absolute;
  top: 20px;
  right: 30px;
  background: transparent;
  border: none;
  font-size: 38px;
  color: #cba135;
  cursor: pointer;
}

.menuSections {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.leftMenu, .rightMenu {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.leftMenu h3, .rightMenu h3 {
  font-size: 24px;
  color: #cba135;
  margin-bottom: 10px;
  font-weight: bold;
}

.leftMenu ul, .rightMenu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.leftMenu li, .rightMenu li {
  font-size: 18px;
  color: #333;
  cursor: pointer;
  transition: 0.3s;
}

.leftMenu li:hover, .rightMenu li:hover {
  color: #cba135;
  transform: translateX(5px);
}

.divider {
  height: 1px;
  background-color: #cba135;
  margin: 30px 0;
  width: 80%;
}

/* -------------------------
   Responsive (Mobile First)
-------------------------- */
@media (max-width: 768px) {
  .heroContent {
    bottom: 30px;
    left: 20px;
    right: 20px;
    text-align: center;
  }

  .heroContent h1 {
    font-size: 26px;
  }

  .heroContent p {
    font-size: 16px;
  }

  .heroButton {
    padding: 10px 20px;
    font-size: 16px;
  }

  .sidebarMenu {
    width: 100%;
    padding: 80px 24px;
    align-items: flex-start;
    justify-content: flex-start;
    overflow-y: auto;
  }

  .menuSections {
    gap: 24px;
  }

  .leftMenu li, .rightMenu li {
    font-size: 16px;
  }

  .closeButton {
    top: 20px;
    right: 20px;
    font-size: 30px;
  }

  .bgVideo {
    height: 100vh;
    object-fit: cover;
  }

  .swiper, .slide {
    height: 100vh !important;
  }
}
