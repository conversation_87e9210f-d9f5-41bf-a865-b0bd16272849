.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.loginContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(192, 192, 192, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(169, 169, 169, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.loginBox {
  background: rgba(248, 249, 250, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(192, 192, 192, 0.2);
  position: relative;
  z-index: 1;
}

.logoSection {
  text-align: center;
  margin-bottom: 2.5rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.logoIcon {
  font-size: 2rem;
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logoText {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.subtitle {
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.01em;
}

.errorMessage {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  text-align: center;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.input {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: #ffffff;
  color: #1a1a1a;
  transition: all 0.2s ease;
  outline: none;
}

.input:focus {
  border-color: #c0c0c0;
  box-shadow: 0 0 0 3px rgba(192, 192, 192, 0.1);
}

.input::placeholder {
  color: #9ca3af;
}

.loginButton {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.loginButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d2d2d, #404040);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.loginButton:active {
  transform: translateY(0);
}

.loginButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loadingSpinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.footer p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 640px) {
  .loginContainer {
    padding: 1rem;
  }
  
  .loginBox {
    padding: 2rem;
  }
  
  .logoText {
    font-size: 1.75rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
}
