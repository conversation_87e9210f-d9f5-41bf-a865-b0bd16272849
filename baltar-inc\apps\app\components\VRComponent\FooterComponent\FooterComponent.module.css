.footer {
    background: #f9f9f9;
    color: #000;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 2rem;
  }
  
  .topSection {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .inspireText {
    font-size: 1.3rem;
    font-weight: 500;
  }
  
  .subscription {
    display: flex;
    gap: 1rem;
  }
  
  .subscription input {
    padding: 0.6rem;
    flex: 1;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  
  .subscription button {
    background: #111;
    color: white;
    padding: 0.6rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .accessibility {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
  }
  
  .switch {
    position: relative;
    display: inline-block;
    width: 42px;
    height: 22px;
  }
  
  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 22px;
    transition: 0.4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
  }
  
  input:checked + .slider {
    background-color: #111;
  }
  
  input:checked + .slider:before {
    transform: translateX(20px);
  }
  
  .linkSection {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    flex-wrap: wrap;
  }
  
  .linkSection div {
    min-width: 160px;
    margin-bottom: 2rem;
  }
  
  .linkSection h4 {
    margin-bottom: 0.6rem;
    font-weight: 600;
  }
  
  .linkSection p {
    margin: 0.2rem 0;
    font-size: 0.9rem;
    cursor: pointer;
  }
  
  .bottomBar {
    border-top: 1px solid #ddd;
    margin-top: 2rem;
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    font-size: 0.85rem;
  }
  
  .socials {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }
  
  .brand {
    margin-top: 0.5rem;
  }
  