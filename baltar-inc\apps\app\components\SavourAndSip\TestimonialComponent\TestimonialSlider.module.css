.testimonialSection {
  padding: 6rem 2rem;
  background: #ffffff;
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 1.25rem;
  color: #4a4a4a;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.sliderWrapper {
  position: relative;
}

.swiper {
  padding-bottom: 3rem;
}

.testimonialCard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  min-height: 400px;
}

.imageSection {
  position: relative;
  overflow: hidden;
}

.testimonialImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(212, 175, 55, 0.2), rgba(244, 228, 166, 0.1));
}

.contentSection {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.quoteIcon {
  font-size: 4rem;
  color: #d4af37;
  font-family: serif;
  line-height: 1;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.quote {
  font-size: 1.25rem;
  color: #1a1a1a;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-style: italic;
  position: relative;
}

.rating {
  margin-bottom: 1.5rem;
}

.star {
  font-size: 1.2rem;
  color: #ddd;
  margin-right: 0.25rem;
  transition: color 0.3s ease;
}

.star.filled {
  color: #d4af37;
}

.authorInfo {
  margin-top: auto;
}

.authorName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.eventDetails {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

/* Swiper Pagination Styles */
.swiper :global(.swiper-pagination) {
  bottom: 0 !important;
}

.swiper :global(.swiper-pagination-bullet) {
  background: #d4af37 !important;
  opacity: 0.3 !important;
  width: 12px !important;
  height: 12px !important;
}

.swiper :global(.swiper-pagination-bullet-active) {
  opacity: 1 !important;
  transform: scale(1.2) !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .testimonialCard {
    grid-template-columns: 1fr;
    min-height: auto;
  }
  
  .imageSection {
    height: 250px;
  }
  
  .contentSection {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .testimonialSection {
    padding: 4rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
  }
  
  .contentSection {
    padding: 1.5rem;
  }
  
  .quote {
    font-size: 1.1rem;
  }
  
  .quoteIcon {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .quote {
    font-size: 1rem;
  }
  
  .authorName {
    font-size: 1rem;
  }
  
  .eventDetails {
    font-size: 0.9rem;
  }
  
  .imageSection {
    height: 200px;
  }
}
