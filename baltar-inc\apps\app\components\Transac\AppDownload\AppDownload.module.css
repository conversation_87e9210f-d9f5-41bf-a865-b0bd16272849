.downloadSection {
  padding: 4rem 2rem;
  background-color: #fefefe;
  color: #111827;
  overflow-x: hidden;
  max-width: 100vw;
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 3rem;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.mockup {
  flex: 1;
  text-align: center;
  min-width: 280px;
  box-sizing: border-box;
}

.image {
  max-width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.1);
}

.content {
  flex: 1;
  min-width: 280px;
  box-sizing: border-box;
}

.content h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.content ul {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.content li {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  position: relative;
  padding-left: 1.5rem;
  color: #374151;
}

.content li::before {
  content: '✔';
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: bold;
}

.badges {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

/* 📱 Mobile Responsive */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    text-align: center;
  }

  .content h2 {
    font-size: 1.6rem;
  }

  .badges {
    justify-content: center;
  }
}
